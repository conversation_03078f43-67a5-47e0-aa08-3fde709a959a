import Model from "../helpers/model";
import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import request from 'request';
import axios from 'axios';
import EmailSender from '../helpers/email.helper'
import kotaniPay from "../helpers/kotani";
import CNGNUtility from "../helpers/CNGNUtility";
import { Network } from "cngn-typescript-library";
import Ogateway from "../helpers/Ogateway";
import Quidax, { QuidaxWithdrawData } from "../helpers/Quidax";
import MudaPayment from "../helpers/MudaPayment";
import HoneyCoin from "../helpers/HoneyCoin"
import BlockchainVerifier from '../helpers/blockchainVerifier';
import {
    WebhookPayload,
    CryptoReceivedWebhook,
    FiatSentWebhook,
    FiatReceivedWebhook,
    GeneralStatusWebhook,
    CryptoWebhookData,
    BankPayment,
    MobileMoneyPayment,
    PayoutRequest,
    PayoutResponse,
    Quote
} from '../interfaces/webhook.interfaces';
import { WebhookFactory } from '../helpers/webhookFactory';
import { WebhookSender } from '../helpers/webhookSender';
import { ProviderService, RateRequest, RateResponse } from '../interfaces/rate.interfaces';


const mailer = new EmailSender()
const cngn = new CNGNUtility()



export interface PaymentMethod {
    payment_method_id: string;
    company_id: string;
    kotani_customer_key?: string | null;
    type: 'bank' | 'mobile_money';
    currency: string | null;
    phone_number: string | null;
    country_code: string | null;
    network: string | null;
    account_name: string;
    bank_name: string | null;
    bank_code: string | null;
    account_number: string;
    bank_address: string | null;
    bank_phone_number: string;
    bank_country: string | null;
    sort_code: string | null;
    swift_code?: string | null;
}

type QuoteResponse = {
    quote_id: string;
    status: string;
    receive_amount: number;
    send_asset: string;
    receive_currency: string;
    send_amount: string;
    ex_rate: number;
    fee: number;
    payment_method_id: string;
    expires_at: string;

    chainInfo?: {
        pay_in_address: string;
        pay_in_memo: string;
    };
}

class Accounts extends Model {

    constructor() {
        super();
    }
    static async LogWebhook(data: any, eventType: string, provider: string) {
        const logData = {
            event_type: eventType,
            provider: provider,
            data: JSON.stringify(data)
        }
        await new Accounts().insertData("lg_received_webhooks", logData);
        return true;
    }



    async changePassword(data: any) {
        const { oldPassword, staff_id, newPassword } = data;
        const hashedOldPassword = this.hashPassword(oldPassword);
        const hashedNewPassword = this.hashPassword(newPassword);

        const existingUser = await this.getLoggedInUser(staff_id, hashedOldPassword);
        if (existingUser.length == 0) {
            return this.makeResponse(401, "Auth error");
        }
        const email = existingUser[0].email
        const user_id = existingUser[0].user_id
        const first_name = existingUser[0].first_name
        const updates: any = {
            password: hashedNewPassword
        };
        await this.updateData('users', `user_id = '${user_id}'`, updates);
        this.sendEmail("CHANGE_PASSWORD", email, first_name);
        return this.makeResponse(200, "success");
    }

    async resetPasswordRequest(data: any) {
        try {
            const { email } = data;

            const existingUser = await this.getUserByEmail(email);
            if (existingUser.length == 0) {
                return this.makeResponse(404, "email not found");
            }


            const user_id = existingUser[0].company_id
            const first_name = existingUser[0].name
            const otp = await this.getOTP(email, user_id.toString());
            this.sendEmail("RESET_PASSWORD_REQUEST", email, first_name, otp);
            return this.makeResponse(200, "success");
        } catch (err) {
            console.log(err)
            return this.makeResponse(203, "Error processing request");

        }
    }

    //webhook from moralis
    async cryptoWebhook(data: any) {
        try {
            console.log("moralisData", data);

            const webhookPayload = await WebhookFactory.createCryptoReceived(data, 'moralis');
            const result = await this.processReceivedWebhook(webhookPayload);
            return this.makeResponse(200, "Webhook processed successfully", result);
        } catch (error) {
            console.error("Error in cryptoWebhook:", error);
            return this.makeResponse(203, "Error processing webhook");
        }



    }


    async resetPassword(data: any) {
        try {
            const { otp, email, newPassword } = data;
            const hashedNewPassword = this.hashPassword(newPassword);

            const otpRs = await this.selectDataQuery("user_otp", `email = '${email}' AND otp = ${otp} `);
            if (otpRs.length == 0) {
                return this.makeResponse(203, "OTP not found");
            }
            const user_id = otpRs[0]['user_id']

            const existingUser = await this.getUserByEmail(email);
            if (existingUser.length == 0) {
                return this.makeResponse(404, "User Id not found" + user_id);
            }
            const first_name = existingUser[0].name
            const updates: any = {
                password: hashedNewPassword
            };
            await this.updateData('company_accounts', `company_id = '${user_id}'`, updates);
            this.sendEmail("RESET_PASSWORD_COMPLETE", email, first_name);
            return this.makeResponse(200, "success");
        } catch (err) {
            console.log(err)
            return this.makeResponse(203, "Error processing request");

        }
    }




    async verifyCode(data: any) {
        const { code } = data;
        const otpRs = await this.selectDataQuery("user_otp", `otp = '${code}' `);
        if (otpRs.length == 0) {
            return this.makeResponse(203, "code not found");
        }
        const user_id = otpRs[0].user_id
        const updates: any = {
            email_verified: 'yes'
        };

        await this.updateData('company_accounts', `company_id = '${user_id}'`, updates);
        return this.makeResponse(200, "User email verified");

    }



    private hashPassword(password: string) {
        const hash = crypto.createHash('sha256');
        return hash.update(password).digest('hex');
    }

    async login(data: any) {
        const { email, password } = data;
        const hashPassword = this.hashPassword(password);

        try {
            // Fetch user by 
            const users = await this.selectDataQuery("company_accounts", `email = '${email}' and password='${hashPassword}'`);
            const user = users.length > 0 ? users[0] : null;

            if (!user) {
                return this.makeResponse(203, "User not found");
            }
            let user_id = user.company_id

            console.log("user_id", user_id)
            const jwts: any = process.env.JWT_SECRET
            const token = jwt.sign({ user_id, company_id: user_id }, jwts, {
                expiresIn: 86400 // 24 hours
            });
            const response = { ...user, jwt: token };
            return this.makeResponse(200, "Login successful", response);
        } catch (error) {
            console.error("Error in login:", error);
            return this.makeResponse(203, "Error logging in");
        }
    }


    // Create a new company account
    async addCompany(data: any) {
        try {
            // Destructure the fields from the payload
            const {
                first_name,
                last_name,
                business_name,
                phone_number,
                email,
                password,      // If you truly need a password
                passport,      // If you want a separate passport field
                account_type,
                country,
                payin_assets,
                transfer_types
            } = data;

            // Combine first_name + last_name
            const full_name = `${first_name} ${last_name}`.trim();

            console.log("DATA", data);

            // Validate required fields (adjust as needed)
            if (!full_name || !email || !password || !account_type) {
                return this.makeResponse(203, "Missing required fields");
            }

            // Check if email already exists
            const companies = await this.selectDataQuery(
                "company_accounts",
                `email='${email}'`
            );
            if (companies.length > 0) {
                return this.makeResponse(203, "Email already exists");
            }

            // Validate password strength (example check)
            if (password.length < 7) {
                return this.makeResponse(203, "Weak password");
            }

            // Hash the password before saving (if you're actually storing a password)
            const hashPassword = this.hashPassword(password);

            const cId = this.generateRandom4DigitNumber()
            // Build the new company object for the 'company_accounts' table
            let newCompany: any = {
                company_id: cId,
                name: full_name,
                business_name,
                phone: phone_number,
                email,
                password: hashPassword,
                user_type: account_type.toLowerCase(),  // standardize
                country
            };



            // Insert the new record in 'company_accounts'
            const insertedCompany = await this.insertData("company_accounts", newCompany);
            console.log(`INSETING`, insertedCompany)

            // If the account is a provider, handle additional data
            if (account_type.toLowerCase() === "provider") {
                // 1) Insert into 'providers' table
                const providerRecord = {
                    name: business_name,
                    approval_status: "inactive",
                    country
                };
                // e.g., insertedProvider might contain the new provider's ID
                const insertedProvider = await this.insertData("providers", providerRecord);


                if (insertedProvider && insertedProvider.insertId && payin_assets?.length > 0) {
                    for (const asset of payin_assets) {
                        await this.insertData("providers_assets", {
                            provider_id: insertedProvider.insertId,
                            asset_type: "payin", // or use a dedicated column name if needed
                            asset: asset,   // rename columns to match your schema
                        });
                    }
                }


            }


            const otp = await this.getOTP(email, cId.toString());
            this.sendEmail("ACCOUNT_CREATION", email, first_name, otp);
            // this.sendEmail("ACCOUNT_CREATION", email, first_name);

            return this.makeResponse(200, "Company added successfully", insertedCompany);
        } catch (error) {
            console.error("Error in addCompany:", error);
            return this.makeResponse(203, "Error adding company");
        }
    }



    // Fetch all companies
    async getCompanies() {
        try {
            const companies = await this.selectDataQuery('company_accounts');
            if (companies.length > 0) {
                return this.makeResponse(200, "Companies fetched successfully", companies);
            } else {
                return this.makeResponse(404, "No companies found");
            }
        } catch (error) {
            console.error("Error in getCompanies:", error);
            return this.makeResponse(203, "Error fetching companies");
        }
    }

    // Create a new service for a company
    async addService(data: any) {
        try {
            console.log(data)
            const { service_name, provider_name, country, chain, company_id, currency } = data;
            const newService = { service_name, provider_name, country, chain, company_id, currency };
            const insertedService = await this.insertData('services', newService);
            return this.makeResponse(200, "Service added successfully", insertedService);
        } catch (error) {
            console.error("Error in addService:", error);
            return this.makeResponse(203, "Error adding service");
        }
    }

    // Fetch services for a specific company
    async getServices(company_id: number) {
        try {
            const services = await this.selectDataQuery("services", `company_id = '${company_id}'`);
            if (services.length > 0) {
                return this.makeResponse(200, "Services fetched successfully", services);
            } else {
                return this.makeResponse(404, "Services not found for the given company");
            }
        } catch (error) {
            console.error("Error in getServices:", error);
            return this.makeResponse(203, "Error fetching services");
        }
    }


    async getAssets(query: string) {
        try {
            let services: any = [];
            if (query) {
                services = await this.callQuery(`select * from accepted_assets where type='${query}'`);
            } else {
                services = await this.callQuery(`select * from accepted_assets`);
            }
            if (services.length > 0) {
                return this.makeResponse(200, "assets fetched successfully", services);
            } else {
                return this.makeResponse(404, "assets not found for the given company");
            }
        } catch (error) {
            console.error("Error in assets:", error);
            return this.makeResponse(203, "Error fetching assets");
        }
    }



    // add a service
    async AcceptService(data: any) {
        try {
            const { company_id, service_id, max_amount } = data;
            const newAsset = { provider_id: company_id, service_id, max_amount, min_amount: 0, };
            const exists: any = await this.callQuery(`select * from service_providers where provider_id='${company_id}' AND service_id='${service_id}' `)
            if (exists.length > 0) {
                return this.makeResponse(200, "Address aready exists");
            }

            const insertedAsset = await this.insertData('service_providers', newAsset);
            return this.makeResponse(200, "Accepted asset added successfully", insertedAsset);
        } catch (error) {
            console.error("Error in addAcceptedAsset:", error);
            return this.makeResponse(203, "Error adding accepted asset");
        }
    }


    async getProviderInfo(company_id: any) {
        try {
            const exists: any = await this.callQuery(`select * from providers where provider_id='${company_id}' `)
            return this.makeResponse(200, "Updated successfully", exists);
        } catch (error) {
            console.error("Error in addAcceptedAsset:", error);
            return this.makeResponse(203, "Error adding accepted asset");
        }
    }
    async updatedRatesUrl(data: any) {
        try {
            const { company_id, rates_endpoint } = data;
            const newAsset = { rates_endpoint };

            const rate: any = await this.getConversionRate(1, "USDT", "USD", company_id)
            const postRate: any = parseFloat(rate).toFixed(2)
            if (postRate == 0) {
                return this.makeResponse(203, "Invalid resonse from the rates endpoint, please read the response format required");
            }
            const updatedService = await this.updateData('providers', `provider_id = '${company_id}'`, newAsset);
            return this.makeResponse(200, "Updated successfully");
        } catch (error) {
            console.error("Error in addAcceptedAsset:", error);
            return this.makeResponse(203, "Error adding accepted asset");
        }
    }

    async DisableService(data: any) {
        try {
            const { company_id, service_id } = data;
            const newAsset = { status: 'inactive' };
            const exists: any = await this.callQuery(`select * from service_providers where provider_id='${company_id}' AND service_id='${service_id}' and status='active' `)
            if (exists.length == 0) {
                return this.makeResponse(200, "service not activated");
            }
            const id = exists[0].id

            const updatedService = await this.updateData('service_providers', `id = '${id}'`, newAsset);
            return this.makeResponse(200, "Updated successfully");
        } catch (error) {
            console.error("Error in addAcceptedAsset:", error);
            return this.makeResponse(203, "Error adding accepted asset");
        }
    }




    // Add an accepted asset for a service
    async addAcceptedAsset(data: any) {
        try {

            const { asset, company_id, address, memo, chain } = data;
            const newAsset = { provider_id: company_id, chain, asset, memo, address };
            const exists: any = await this.callQuery(`select * from provider_addresses where provider_id='${company_id}' AND asset='${asset}' and status='active'`)
            if (exists.length > 0) {
                return this.makeResponse(200, "Address aready exists");
            }

            const insertedAsset = await this.insertData('provider_addresses', newAsset);
            return this.makeResponse(200, "Accepted asset added successfully", insertedAsset);
        } catch (error) {
            console.error("Error in addAcceptedAsset:", error);
            return this.makeResponse(203, "Error adding accepted asset");
        }
    }


    async getProviderAddresses(company_id: any) {
        const exists: any = await this.callQuery(`select * from provider_addresses where provider_id='${company_id}' and status='active'`)
        return this.makeResponse(200, "failed", exists);
    }




    // Fetch all accepted assets for a service
    async getAcceptedAssets(service_id: number) {
        try {
            const assets = await this.selectDataQuery("accepted_assets", `service_id = '${service_id}'`);
            if (assets.length > 0) {
                return this.makeResponse(200, "Accepted assets fetched successfully", assets);
            } else {
                return this.makeResponse(404, "No accepted assets found for the given service");
            }
        } catch (error) {
            console.error("Error in getAcceptedAssets:", error);
            return this.makeResponse(203, "Error fetching accepted assets");
        }
    }

    // Update a service's details
    async updateService(service_id: number, newData: any) {
        try {
            const updatedService = await this.updateData('services', `service_id = '${service_id}'`, newData);
            return this.makeResponse(200, "Service updated successfully", updatedService);
        } catch (error) {
            console.error("Error in updateService:", error);
            return this.makeResponse(203, "Error updating");
        }
    }

    async save_log(quote_id: string, hash: string, asset_code: string, asset_amount: number, currency: string, send_amount: string, account_number: string, asset_issuer: string, data: any) {
        try {
            const sql = {
                hash: hash,
                quote_id,
                received_asset: asset_code,
                amount: asset_amount,
                account_number,
                send_amount: send_amount,
                send_currency: currency,
                contract_address: asset_issuer,
                req_body: data
            };
            console.log(`insertData`)

            const result = await this.insertData("pay_log", sql);
            console.log(`Insert Result: ${result}`); // Debugging line
            return true;
        } catch (error) {
            console.log(`Error in save_log: ${error}`);
            return false;
        }
    }

    async update_log(hash: string, data: any, status: "", ext_trans_id: string = '') {
        try {
            const sql = {
                resp_body: data,
                ext_trans_id,
                status
            };

            const condition = `hash='${hash}'`; // Add quotes around the hash value

            const result = await this.updateData("pay_log", condition, sql);
            console.log(`Update Result: ${result}`); // Debugging line
            return true;
        } catch (error) {
            console.log(`Error in update_log: ${error}`);
            return false;
        }
    }


    async callback(data: any) {
        try {
            console.log(`callBack`, data)
            const { transId, hash, status, event_type } = data;
            const allowedStatus = ["SUCCESSFUL", "INITIATED", "PENDING", "ONHOLD"]
            if (!allowedStatus.includes(status)) {
                return this.makeResponse(203, "Status not allowed");
            }

            //query the hash to validate that it was paid depending on the chain
            const transInfo = await this.selectDataQuery(`quotes`, `transId = '${transId}'`)
            if (transInfo.length == 0) {
                return this.makeResponse(404, "transaction not found");
            }
            const singleTransaction = transInfo[0]
            const tStatus = singleTransaction.status
            const pay_in_status = singleTransaction.pay_in_status

            if (tStatus == "SUCCESSFUL" || tStatus == "EXPIRED" || tStatus == "FAILED") {
                return this.makeResponse(203, "transaction status is " + tStatus);
            }


            if (event_type === 'CHAIN_RECEIVED') {
                if (pay_in_status != "PENDING") {
                    return this.makeResponse(203, "transaction pay in status is " + pay_in_status);
                }
                await this.updateData('quotes', `transId = '${transId}'`, { "pay_in_status": status, "hash": hash });
            } else if (event_type === 'PAYOUT') {
                await this.updateData('quotes', `transId = '${transId}'`, { status });
            }

            return this.makeResponse(200, "Status updated");
        } catch (error) {
            console.error("Error in updateTransaction:", error);
            return this.makeResponse(500, "Error updating transaction");
        }
    }

    async confirmPayment(data: any) {
        try {
            console.log(`confirmPayment`, data)
            const { hash, trans_id } = data;

            //query the hash to validate that it was paid depending on the chain
            const transInfo = await this.selectDataQuery(`quotes`, `transId = '${trans_id}'`)
            if (transInfo.length == 0) {
                return this.makeResponse(404, "transaction not found");
            }

            const singleTransaction = transInfo[0]
            const tStatus = singleTransaction.status
            const pay_in_status = singleTransaction.pay_in_status
            const savedHash = singleTransaction.hash
            const transId = singleTransaction.transId
            if (tStatus == "SUCCESSFUL" || tStatus == "EXPIRED" || tStatus == "FAILED") {
                return this.makeResponse(203, "transaction status is " + tStatus);
            }



            // Verify the blockchain transaction
            const blockchainVerifier = new BlockchainVerifier();
            const chain = singleTransaction.chain || "XLM"; // Default to XLM if not specified



            //  if (savedHash != hash) {
            const verificationResult = await blockchainVerifier.verifyTransaction(hash, chain, singleTransaction.send_amount);

            if (!verificationResult.isValid) {
                return this.makeResponse(203, `Blockchain verification failed: ${verificationResult.error}`);
            }

            // Verify the transaction amount matches
            if (verificationResult.amount && parseFloat(verificationResult.amount) < parseFloat(singleTransaction.send_amount)) {
                return this.makeResponse(203, "Transaction amount is less than expected");
            }

            // Verify the recipient address matches
            if (verificationResult.to && verificationResult.to.toLowerCase() !== singleTransaction.receiver_address.toLowerCase()) {
                return this.makeResponse(203, "Transaction recipient address doesn't match");
            }

            if (pay_in_status != "SUCCESSFUL") {
                await this.updateData('quotes', `transId = '${transId}'`, { pay_in_status: 'SUCCESSFUL' });
            }
            //  }


            const payoutData = {
                transId: trans_id,
                hash,
                chainInfo: {
                    hash,
                    asset_amount: singleTransaction.send_amount,
                    asset_code: singleTransaction.send_asset,
                    to_address: singleTransaction.receiver_address,
                    from_address: singleTransaction.sending_address,
                    chain: chain,
                    contract_address: singleTransaction.provider_address || ""
                },
                accountInfo: {
                    account_name: singleTransaction.bank_name || "",
                    account_number: singleTransaction.account_number
                },
                payOutCurrency: singleTransaction.receive_currency,
                payoutAmount: singleTransaction.receive_amount
            };

            const response = await this.requestPayout(payoutData);
            console.log(`requestPayout`, response);
            return response;

        } catch (error) {
            console.error("Error in confirmPayment:", error);
            return this.makeResponse(500, "Error updating transaction");
        }
    }

    async requestPayout(data: any) {
        console.log(`requestPayout`, data)
        // Extract values from the nested objects in the payload
        const hash = data.chainInfo.hash;
        const transId = data.transId; // Original transaction id (if needed)
        const asset_amount = data.chainInfo.asset_amount;
        const asset_code = data.chainInfo.asset_code;
        const to_address = data.chainInfo.to_address;
        const from_address = data.chainInfo.from_address;
        const accountInfo: any = data.accountInfo;
        const acc_name = accountInfo.account_name;
        const account_number = accountInfo.account_number;
        const chain = data.chainInfo.chain;
        const contract_address = data.chainInfo.contract_address;
        const currency = data.payOutCurrency;  // Use payOutCurrency for the payout currency
        let payout_amount = data.payoutAmount.toString(); // Ensure it's a string


        try {

            const transInfo = await this.selectDataQuery(`quotes`, `transId = '${transId}'`)
            if (transInfo.length == 0) {
                return this.makeResponse(404, "transaction not found");
            }
            const singleTransaction = transInfo[0]
            const tStatus = singleTransaction.status
            const pay_in_status = singleTransaction.pay_in_status
            payout_amount = singleTransaction.receive_amount

            if (pay_in_status != "SUCCESSFUL") {
                return this.makeResponse(205, "Transaction not received");
            }


            const rsp = await this.selectDataQuery("pay_log", `quote_id='${transId}'`);
            if (rsp.length > 0) {
                return this.makeResponse(203, "Transaction already processed");
            }

            // Save a log with the received payload details
            await this.save_log(
                transId,
                hash,
                asset_code,
                asset_amount,
                currency,
                payout_amount,
                account_number,
                contract_address,
                JSON.stringify(data)
            );

            console.log("InfoPlate", data);

            // (Optional) You may include additional chain-specific logic here.
            // For example, if (chain.toLowerCase() == "bsc") { ... }

            // Retrieve additional transaction info from previous logs if needed.
            const txInfo = rsp[0];

            await this.updateData('pay_log', `quote_id = '${transId}'`, { status: 'INITIATED' });
            let response: any = null;
            if (currency === "UGX") {
                response = await MudaPayment.makePayout(transId, payout_amount, account_number);
                console.log('UGX Transaction:', response);

            } else if (currency == "GHS") {
                response = await Ogateway.sendToMobile(transId, payout_amount, "JAMES OKELLO", account_number)
                console.log('GHS Transaction:', response);
            } else {
                console.log('UN SUPPORYTED:', response);
                return this.makeResponse(201, "not suported");
            }

            console.log("LIQU_RAIL", response.data);

            // Check if the API response indicates success
            if (response.status == 200 || response.status == 202) {
                await this.updateData('pay_log', `quote_id = '${transId}'`, { status: 'SUCCESSFUL' });
                await this.update_log(hash, JSON.stringify(response.data), transId, "SUCCESSFUL");
            } else {
                await this.updateData('pay_log', `quote_id = '${transId}'`, { status: 'FAILED' });
                await this.update_log(hash, JSON.stringify(response.data), transId, "FAILED");
                console.error("Failed to send liquidity:", response.data);
            }


            return this.makeResponse(200, "Service updated successfully");
        } catch (error) {
            await this.update_log(hash, error, "", "FAILED");
            console.error("Error in updateTransaction:", error);
            return this.makeResponse(203, "Error updating service");
        }
    }


    async getPendingQuotes(userId: any) {
        return await this.callQuery(`select * from quotes where company_id='${userId}' and status='PENDING' `)
    }
    async getPendingQuotesFilter(data: any) {
        const { company_id, send_asset, send_amount, receive_currency, chain, service_id } = data
        return await this.callQuery(`select * from quotes where company_id='${company_id}' and send_asset='${send_asset}' and send_amount='${send_amount}' and receive_currency='${receive_currency}' and chain='${chain}' and service_id='${service_id}' and status='PENDING' `)
    }

    async cancelQuote(id: string, userId: any) {
        const addExists: any = await this.callQuery(`select * from quotes where transId='${id}' and status='PENDING' `)

        if (addExists.length == 0) {
            return this.makeResponse(203, "PENDING quote not found");
        }
        const updates = {
            status: 'CANCELLED',
            narration: 'CANCELLED'
        };
        await this.updateData('quotes', `transId = '${id}'`, updates);

        // Send webhook for cancelled quote
        await WebhookSender.send(id);

        return this.makeResponse(200, "Quote cancelled");
    }
    async verifyAccount(data: any) {

        const resp = {
            accountName: "",
            bank_code: "2345",
            isvalid: true
        }
        return this.makeResponse(200, "succcess", resp);

    }



    async getQuote(data: any) {
        console.log(`getQuote`, data)
        let { receive_currency, status, send_amount, receiver_address, sending_address, send_asset } = data;
        receiver_address = receiver_address.toLowerCase();
        sending_address = sending_address.toLowerCase();

        // Construct SQL query with lowercased addresses
        const query = `
            SELECT * FROM quotes 
            WHERE send_asset = '${send_asset}' 
            AND receive_currency = '${receive_currency}' 
            AND LOWER(receiver_address) = '${receiver_address}' 
            AND LOWER(sending_address) = '${sending_address}'
            AND status='${status}'
            AND send_amount='${send_amount}'
        `;
        const chainInfo: any = await this.callQuery(query);
        if (chainInfo.length > 0) {
            return this.makeResponse(200, "success", chainInfo[0]);
        } else {
            return this.makeResponse(404, "not found");

        }
    }



    async confirmBookedRate(data: any) {
        try {
            const {
                company_id,
                service_id,
                reference_id,
                payment_method_id,
                sending_address,
                source,
                quote_id
            } = data


            const quoteLogResults: any = await this.callQuery(
                `SELECT * FROM quote_log WHERE quoteId = '${quote_id}' LIMIT 1`
            );
            if (quoteLogResults.length === 0) {
                return this.makeResponse(404, 'Booked quote not found');
            }

            const quote = quoteLogResults[0];
            const postData = {
                provider_id: quote.providerId,
                book_quote_id: quote_id,
                reference_id,
                providerQuoteId: quote.providerQuoteId,
                asset_code: quote.asset_code,
                receive_currency: quote.currency,
                send_amount: parseFloat(quote.cryptoAmount),
                ex_rate: parseFloat(quote.quotedPrice),
                sending_address: sending_address,
                company_id: company_id,
                service_id: quote.service_id,
                payment_method_id: payment_method_id,
                source: "exchange"
            };

            return await this.saveQuote(postData);

        } catch (err) {
            console.error("Error in confirmBookedRate:", err);
            return this.makeResponse(500, "Internal server error while confirming booked rate");
        }
    }





    async saveQuote(data: any) {
        try {

            const { provider_service_id, source, reference_id, sending_address, company_id, send_amount, receive_currency, payment_method_id, book_quote_id } = data
            let transId = this.getRandomString()
            console.log(`saveQuote`, data)


            if (book_quote_id != "" && book_quote_id != null && book_quote_id != "undefined") {
                transId = book_quote_id
            }
            let service_id = data.service_id || ""
            let provider_id = data.provider_id || ""
            let transaction_type = data.transaction_type || "off_ramp"


            const supportedCurrencyRamps = ["off_ramp", "on_ramp"]
            if (!supportedCurrencyRamps.includes(transaction_type)) {
                return this.makeResponse(203, "Not supported transaction type");
            }


            if (data.provider_service_id) {
                const service: any = await this.callQuery(`select * from service_providers where provider_service_id='${data.provider_service_id}'`)
                if (service.length == 0) {
                    return this.makeResponse(203, "Service not found");
                }
                service_id = service[0].service_id
                provider_id = service[0].provider_id
            }
            // check if quote exists
            const quoteExists: any = await this.callQuery(`select * from quotes where transId='${transId}'`)
            if (quoteExists.length > 0) {
                return this.makeResponse(203, "Quote already exists");
            }

            let asset_code = data.asset_code
            let chain = await this.getchain(asset_code)
            const send_asset = await this.getAssetCode(asset_code)
            if (!send_asset) {
                return this.makeResponse(203, "Asset code not supported");
            }
            let evmAddress = ""
            let privateKey = ""

            const quoteQuery = {
                company_id,
                send_asset,
                send_amount,
                receive_currency,
                chain,
                service_id
            }
            const pendingQutes: any = await this.getPendingQuotesFilter(quoteQuery);
            if (pendingQutes.length > 0) {
                return this.makeResponse(405, "You can't create a new quote when you have pending quotes, please cancel the existing ones first", pendingQutes);
            }

            const chainInfo: any = await this.callQuery(`select * from accepted_assets where asset_code='${asset_code}'  and chain='${chain}' `)
            if (chainInfo.length == 0) {
                return this.makeResponse(203, "coin not suported");
            }
            // const chain = chainInfo[0].chain

            const addExists: any = await this.callQuery(`select * from addresses where address='${sending_address}' and chain='${chain}' `)

            if (addExists.length == 0 && source == "wallet") {
                return this.makeResponse(203, "Address not saved, please save this sending first");
            }



            const getprovider: any = await this.getprovider(provider_id, service_id);
            if (getprovider.length == 0) {
                return this.makeResponse(203, "provider and service id not match");
            }
            console.log(`getprovider`, getprovider)
            const max_amount = getprovider[0].max_amount
            const min_amount = getprovider[0].min_amount
            const service_code = getprovider[0].service_code

            if (send_amount < min_amount) {
                return this.makeResponse(203, `Min quote amount is ${min_amount}`);
            }
            if (send_amount > max_amount) {
                return this.makeResponse(203, `Maximum quote amount is ${max_amount}`);
            }

            const paymentMethod = await this.getPaymentMethodById(payment_method_id)


            if (paymentMethod.length == 0) {
                return this.makeResponse(203, "Payout method not found");

            }
            const { type, phone_number, country_code, network, account_name, } = paymentMethod[0]
            let account_number = phone_number;

            const { blockchainFee, mudaFee, totalFees, payableAmount } = await this.calculateMudaFee(chain, send_amount)
            console.log(`blockchainFees::1`, blockchainFee, mudaFee, totalFees, payableAmount)

            const rateReq: RateRequest = {
                "asset_code": asset_code,
                "currency": receive_currency,
                "amount": payableAmount,
                "provider_id": provider_id,
                "service_code": service_code,
                "transaction_type": transaction_type
            }

            console.log(`blockchainFees::2`, rateReq)
            const rate: any = await this.getRate(rateReq, transId)
            if (!rate.data.fiatAmount) {
                return this.makeResponse(203, "Rate not found");
            }
            const receive_amount = rate.data.fiatAmount
            const fee = rate.data.fee
            const ex_rate = rate.data.quotedPrice

            let externalRefId = ""
            let provider_memo = ""

            const addressInfo: any = await this.generate_address(provider_id, send_asset, chain)
            console.log(`keypair====>`, addressInfo)
            const { keypair, memo } = addressInfo

            if (keypair) {
                evmAddress = keypair
               // externalRefId = memo
            } else {
                return this.makeResponse(203, "Failed to generate address");
            }
            const mudaAddress = await this.getMudaAddress(chain)
            if (mudaAddress == false) {
                return this.makeResponse(203, "Muda address not found");
            }


            const addressMapping = await this.insertData('quote_address_mapping', {
                quote_id: transId,
                muda_address: mudaAddress,
                provider_id,
                provider_address: evmAddress,
                memo: externalRefId
            })
            if (addressMapping == false) {
                return this.makeResponse(203, "Address mapping not saved");
            }

            const expiresAt = new Date(Date.now() + 1000 * 60 * 120)
                .toISOString()
                .slice(0, 19)
                .replace('T', ' ');
            console.log(`expiresAt`, expiresAt)

            // Generate new send amount if needed
            const new_send_amount = await this.generateSendAmount(send_asset, send_amount);
            //  const new_send_amount = source == "wallet" ? send_amount : await this.generateSendAmount(send_asset, send_amount);
            const newTransaction = {
                company_id,
                provider_id,
                transId,
                send_asset: send_asset,
                client_reference_id: reference_id,
                send_amount: new_send_amount,
                receive_currency,
                receive_amount,
                ex_rate,
                account_number,
                service_id,
                payable_amount: payableAmount,
                sending_address,
                fee: totalFees,
                fee_currency: send_asset,
                receiver_address: mudaAddress,
                status: 'PENDING',
                provider_ref_id: externalRefId,
                provider_address: evmAddress,
                provider_memo,
                expires_at: expiresAt,
                payment_method_id
            };

            console.log(`newTransaction`, newTransaction)



            const insertedTransaction = await this.insertData('quotes', newTransaction);
            if (insertedTransaction == false) {
                return this.makeResponse(300, "Transaction not saved");

            }
            console.log('Inserted Transaction ID:', insertedTransaction);

            try {

                await this.logMudaFee({
                    quote_id: transId,
                    provider_service_id,
                    muda_fee: mudaFee,
                    thirdparty_fee: totalFees,
                    thirdparty_quote: rate.data?.providerQuoteId ?? '',
                    thirdparty_rate: rate.data?.quotedPrice ?? '',
                    blockchain_fee: blockchainFee,
                    blockchain_fee_asset: send_asset,
                    rate: rate.data?.quotedPrice ?? ''
                });
            } catch (feeErr) {
                console.error('Error computing/logging MUDA fee:', feeErr);
            }



            const quoteResponse: QuoteResponse = {
                quote_id: transId,
                status: 'PENDING',
                send_asset: send_asset,
                send_amount: send_asset,
                receive_currency: receive_currency,
                ex_rate: ex_rate,
                fee: totalFees,
                receive_amount: receive_amount,
                payment_method_id: payment_method_id,
                expires_at: expiresAt
            }
            const payInfo = {
                send_asset: send_asset,
                send_amount: new_send_amount,
                pay_in_address: mudaAddress,
                pay_in_memo: memo
            }
            const reponseObj = {
                ...quoteResponse,
                chainInfo: payInfo
            }
            return this.makeResponse(200, "Quote created successfully", reponseObj);

        } catch (error: any) {
            console.error('Error inserting transaction:', error);
        }
        return this.makeResponse(400, "Error generating an invoice");
    }







    // Unified webhook processor
    async processReceivedWebhook(webhookPayload: any): Promise<any> {
        try {
            console.log(`Processing webhook from ${webhookPayload.provider}:`, webhookPayload);

            const transactionInfo = await this.findTransactionByWebhook(webhookPayload);
            if (!transactionInfo) {
                return this.makeResponse(404, "Transaction not found");
            }

            const normalizedStatus = webhookPayload.status;
            console.log(`normalizedStatus`, normalizedStatus)

            //  const normalizedStatus = this.normalizeWebhookStatus(webhookPayload.status, webhookPayload.provider);

            // Update transaction status
            await this.updateWebhookTransactionStatus(transactionInfo, normalizedStatus, webhookPayload);

            // Send outgoing webhook based on current status
         //   await WebhookSender.send(transactionInfo.transId);

            // If payment is successful, process payout
            if (normalizedStatus === 'SUCCESSFUL') {
                const payoutRequest: PayoutRequest = {
                    transId: transactionInfo.transId,
                    chain: transactionInfo.chain,
                    providerId: transactionInfo.provider_id,
                    serviceId: transactionInfo.service_id,
                    currency: transactionInfo.receive_currency,
                    asset_code: transactionInfo.send_asset,
                    amount: transactionInfo.payable_amount,
                    fiatAmount: transactionInfo.receive_amount,
                    accountNumber: transactionInfo.account_number,
                    paymentMethodId: transactionInfo.payment_method_id
                };

                const payoutResult = await this.processSwapRequest(payoutRequest);

                // Send webhook after payout completion (status will be updated in processPayout)
                await WebhookSender.send(transactionInfo.transId);

                return this.makeResponse(200, "Webhook processed and payout initiated", payoutResult);
            }

            return this.makeResponse(200, "Webhook processed successfully");
        } catch (error) {
            console.error("Error processing webhook:", error);
            return this.makeResponse(500, "Error processing webhook");
        }
    }

    // Find transaction based on webhook data
    private async findTransactionByWebhook(webhook: any): Promise<any> {
        let query = '';
        const eventType = webhook.eventType;
        let receivedData: any = null;
        if (eventType === 'crypto_received') {
            receivedData = webhook as CryptoReceivedWebhook;
        } else {
            receivedData = webhook
        }
        this.saveApiLog(receivedData, 'findTransactionByWebhook');
        console.log(`receivedData`, receivedData)
        switch (receivedData.provider) {
            case 'muda':
                query = `transId = '${receivedData.reference_id || receivedData.transactionId}' AND status='PENDING'`;
                break;
            case 'fireblocks':
            case 'quidax':
                query = `send_amount = ${receivedData.amount} AND LOWER(receiver_address) = LOWER('${receivedData.to_address}')  AND send_asset = '${receivedData.asset_code}' AND status='PENDING'`;
                break;
            case 'kotani':
            case 'general':
            case 'moralis':
                query = `send_amount = '${webhook.amount}' AND status='PENDING'`;
                break;
            case 'stellar':
                query = `send_amount = ${receivedData.amount} AND id = '${receivedData.memo}' AND LOWER(receiver_address) = LOWER('${receivedData.to_address}')  AND send_asset = '${receivedData.asset_code}' AND status='PENDING'`;
                break;
            default:
                query = `send_amount = ${receivedData.amount} AND LOWER(sending_address) = LOWER('${receivedData.from_address}') AND LOWER(receiver_address) = LOWER('${receivedData.to_address}')  AND send_asset = '${receivedData.asset_code}' AND status='PENDING'`;
        }
        this.saveApiLog(query, 'findTransactionByWebhook');

        const result = await this.selectDataQuery('quotes', query);
        return result.length > 0 ? result[0] : null;
    }



    // Update transaction status with webhook data
    private async updateWebhookTransactionStatus(transaction: any, status: string, webhook: WebhookPayload): Promise<void> {
        const updates: any = {
            pay_in_status: status
        };

        // Only update hash for crypto webhooks that have a hash property
        if ('hash' in webhook && webhook.hash) {
            updates.hash = webhook.hash;
        }

        updates.narration = `Provider: ${webhook.provider}, Status: ${webhook.status}, Type: ${webhook.eventType}`;

        await this.updateData('quotes', `transId = '${transaction.transId}'`, updates);
    }




    async LogOperation(quote_id: string, provider: string, action: string, data: any) {
        await this.insertData('quote_audit_log', {
            quote_id,
            provider,
            action,
            data: typeof data === 'object' ? JSON.stringify(data) : data
        })
    }

    async processSwapRequest(payoutRequest: PayoutRequest) {
        try {
            console.log(`Processing swap and withdraw to the provider:`, payoutRequest);

            let swapResponse: any = null;

            const stableAssets = ['USDT', 'USDC']
            const chain = payoutRequest.chain?.toLocaleLowerCase() || ''
            const sentAsset = payoutRequest.asset_code.toLocaleLowerCase()
            let path = [sentAsset, 'usdt']
            const getlogmapping = await this.selectDataQuery(`quote_address_mapping`, `quote_id = '${payoutRequest.transId}'`)
            const mudaAddress = getlogmapping[0].muda_address
            const providerAddress = getlogmapping[0].provider_address

            // Detect if the providerAddress is a Tron or BSC address
            // Tron addresses typically start with 'T' and are 34 chars, BSC are 42 chars and start with '0x'
            let addressType = '';
            if (typeof providerAddress === 'string') {
                if (providerAddress.startsWith('T') && providerAddress.length === 34) {
                    addressType = 'tron';
                } else if (providerAddress.startsWith('0x') && providerAddress.length === 42) {
                    addressType = 'bsc';
                } else {
                    addressType = 'unknown';
                }
            }
            const providerId = payoutRequest.providerId
            console.log(`Detected provider address type:`, addressType);
            const memo = getlogmapping[0].memo
            console.log(`payoutRequest`, payoutRequest)

            let settlementAddress = providerAddress
            if (providerId == '2') {
                const response = await this.createKotaniOffRamp(payoutRequest.transId)
                if (response != null) {
                    settlementAddress = response
                }
            }
            const swappableChains = ['tron', 'bsc']
            const nowSwap = ['stellar']
            // const isSwappable = swappableChains.includes(chain)
            const isNowSwap = nowSwap.includes(chain)

            const swapCurrency = ['ugx', 'ghs', 'kes', 'ngn']
            const nowSwapCurrency = ['ugx']

            let isSwappable = false;
            if (isNowSwap) {
                isSwappable = true;
            }
            const currency = payoutRequest.currency.toLowerCase()
            const isSwapCurrency = swapCurrency.includes(currency)
            if (isSwapCurrency) {
                isSwappable = true;
            } else {
                isSwappable = false;
            }

            console.log(`isSwappable`, isSwappable)


            if (sentAsset.toLowerCase() == 'cngn' || providerId == '4') {
                if (sentAsset == 'cngn') {
                    const swap1 = await this.swapQuidax(sentAsset, 'ngn', Number(payoutRequest.amount), this.getRandomString())
                    this.LogOperation(payoutRequest.transId, 'quidax', 'SWAP1', swap1)

                    if (currency.toLowerCase() != 'ngn') {
                        const swap2 = await this.swapQuidax('ngn', 'usdt', Number(payoutRequest.amount), this.getRandomString())
                        this.LogOperation(payoutRequest.transId, 'quidax', 'SWAP2', swap2)
                    }
                }

                if (payoutRequest.currency.toLowerCase() == 'ngn') {
                    const swap1 = await this.swapQuidax(sentAsset, 'ngn', Number(payoutRequest.amount), this.getRandomString())
                    this.LogOperation(payoutRequest.transId, 'quidax', 'SWAP1', swap1)

                    return await this.processPayout(payoutRequest);

                } else {
                    this.LogOperation(payoutRequest.transId, 'quidax', 'WITHDRAW', payoutRequest.amount)
                    const response = await this.QuidaxWithdraw(settlementAddress, 'usdt', Number(payoutRequest.amount), "qw_" + payoutRequest.transId, "");
                    this.LogOperation(payoutRequest.transId, 'quidax', 'WITHDRAW_RESPONSE', response)

                    if (response.status === 'success') {
                        return await this.processPayout(payoutRequest);
                    }
                }
            } else {
                return this.processPayout(payoutRequest);
            }

        } catch (error) {
            console.error("Error processing payout:", error);
            return {
                success: false,
                status: 'FAILED',
                message: 'Payout processing error'
            };
        }




    }

    async swapQuidax(from: string, to: string, amount: number, transId: string) {
        const quoteResponse: any = await this.getQuidaxQuote(from, to, amount);
        this.LogOperation(transId, 'quidax', 'QUOTATION_REQUEST', { from, to, amount })
        const provider_quote_id = quoteResponse?.providerQuoteId || "";
        const swapResponse = await Quidax.confirmCryptoFiatSwap('me', provider_quote_id);
        this.LogOperation(transId, 'quidax', 'QUOTATION_RESPONSE', swapResponse)
    }

    async QuidaxWithdraw(escrowAddress: string, currency: string, amount: number, transId: string, bankCode: string) {
        const withdrawData: QuidaxWithdrawData = {
            user: 'me',
            currency: currency,
            amount: amount.toString(),
            transaction_note: transId,
            narration: 'MUDA PAY',
            fund_uid: escrowAddress,
            fund_uid2: bankCode,
            reference: transId,
            network: 'trc20'
        }
        this.LogOperation(transId, 'quidax', 'QUIDAX_WITHDRAW_REQUEST', withdrawData)
        const response = await Quidax.createWithdraw(withdrawData);
        this.LogOperation(transId, 'quidax', 'QUIDAX_WITHDRAW_RESPONSE', response)

        let resp: any = null;
        if (response.status === 'success') {
            resp = {
                status: 200,
                message: response.message,
                transactionId: response.data.id
            }
        } else {
            resp = {
                status: 203,
                message: response.message
            }
        }
        return resp
    }

    async processPayout(payoutRequest: PayoutRequest): Promise<PayoutResponse> {
        try {

            console.log(`Waiting for 30 seconds:`, payoutRequest.transId);
            await new Promise(resolve => setTimeout(resolve, 30000));
            console.log(`Processing payout:`, payoutRequest.transId);
            this.LogOperation(payoutRequest.transId, 'processPayou', 'PROCESS_PAYOUT_REQUEST', payoutRequest)

            // Get payment method details if needed
            let paymentMethod = null;
            if (payoutRequest.paymentMethodId) {
                const paymentMethods = await this.getPaymentMethodById(payoutRequest.paymentMethodId);
                paymentMethod = paymentMethods.length > 0 ? paymentMethods[0] : null;
            }

            const quoteResponse = await this.selectDataQuery(`quote_log`, `quoteId = '${payoutRequest.transId}'`);
            const quotation_id = quoteResponse[0].providerQuoteId;

            let response: any = null;
            const { currency, transId, accountNumber, fiatAmount } = payoutRequest;


            // Route to appropriate payment processor based on currency
            switch (currency) {
                case 'UGX':
                    response = await MudaPayment.makePayout(transId, parseFloat(fiatAmount), accountNumber);
                    break;

                case 'GHS':
                    const accountName = paymentMethod?.account_name || "MUDA PAY USER";
                    response = await Ogateway.sendToMobile(transId, parseFloat(fiatAmount), accountName, accountNumber);
                    break;

                case 'NGN':
                    const bankCode = paymentMethod.bank_code;
                    response = await this.QuidaxWithdraw(accountNumber, 'ngn', Number(fiatAmount), transId, bankCode);
                    this.LogOperation(transId, 'quidax', 'QUIDAX_CONVERT_RESPONSE', response)
                    break;
                case 'ZAR':
                    await this.updateData('quotes', `transId = '${transId}'`, { status: 'PENDING_PROVIDER' });
                    break;
                case 'KES':
                    await this.updateData('quotes', `transId = '${transId}'`, { status: 'PENDING_PROVIDER' });
                    break;

                default:
                    return {
                        success: false,
                        status: 'FAILED',
                        message: `Currency ${currency} not supported`
                    };
            }

            // Process response and update transaction
            if (response && (response.status === 200 || response.status === 202)) {
                await this.updateData('quotes', `transId = '${transId}'`, { status: 'SUCCESSFUL' });
                this.LogOperation(transId, 'processPayout', 'PROCESS_PAYOUT_SUCCESS', response)
                // Send webhook for successful payout
                await WebhookSender.send(transId);

                return {
                    success: true,
                    status: 'SUCCESSFUL',
                    message: 'Payout processed successfully',
                    transactionId: transId,
                    providerResponse: response.data
                };
            } else {
                await this.updateData('quotes', `transId = '${transId}'`, { status: 'ONHOLD' });
                this.LogOperation(transId, 'processPayout', 'PROCESS_PAYOUT_ONHOLD', response)
                // Send webhook for failed payout
                await WebhookSender.send(transId);

                return {
                    success: false,
                    status: 'ONHOLD',
                    message: 'Payout failed',
                    transactionId: transId,
                    providerResponse: response?.data
                };
            }

        } catch (error) {
            console.error("Error processing payout:", error);

            await this.updateData('quotes', `transId = '${payoutRequest.transId}'`, { status: 'FAILED' });

            // Send webhook for payout error
            await WebhookSender.send(payoutRequest.transId);

            return {
                success: false,
                status: 'FAILED',
                message: 'Payout processing error'
            };
        }
    }

    // Webhook methods - now using WebhookFactory
    async webhooksMuda(data: any) {
        const webhookPayload = WebhookFactory.createFiatSent(data, 'muda');
        return await this.processReceivedWebhook(webhookPayload);
    }

    async webhooksFireblocks(pdata: any) {
        if (pdata.event === 'TRANSACTION_STATUS_UPDATED') {
            const webhookPayload = await WebhookFactory.createCryptoReceived(pdata.data, 'fireblocks');
            return await this.processReceivedWebhook(webhookPayload);
        }
        return this.makeResponse(200, "Event not processed");
    }

    async webhook_stellar(pdata: any) {
        const getRandomString = this.getRandomString();
        this.LogOperation(getRandomString, 'stellar', 'STELLAR_WEBHOOK', pdata)
        const webhookPayload = await WebhookFactory.createCryptoReceived(pdata.data, 'stellar');
        this.LogOperation(getRandomString, 'stellar', 'STELLAR_WEBHOOK_DEPOSIT', webhookPayload)
        return await this.processReceivedWebhook(webhookPayload);
    }

    async webhooksQuidax(pdata: any) {
        try {
            const { event } = pdata;
            const type = pdata.data.type;
            const getRandomString = this.getRandomString();
            this.LogOperation(getRandomString, 'quidax', 'QUIDAX_WEBHOOK', pdata.data)

            if (event === 'deposit.successful') {
                if (type === 'coin_address') {
                    const webhookPayload = await WebhookFactory.createCryptoReceived(pdata.data, 'quidax');
                    this.LogOperation(getRandomString, 'quidax', 'QUIDAX_WEBHOOK_DEPOSIT', webhookPayload)

                    return await this.processReceivedWebhook(webhookPayload);
                } else {
                    const webhookPayload = WebhookFactory.createFiatReceived(pdata.data, 'quidax');
                    return await this.processReceivedWebhook(webhookPayload);
                }
            }

            return this.makeResponse(200, "Event not processed");
        } catch (error) {
            console.log("Quidax webhook error:", error);
            return this.makeResponse(200, "Error processed");
        }
    }

    async webhookStella(pdata: any) {
        const { event } = pdata;
        const type = pdata.type;
        if (event === 'token_payment') {
            const webhookPayload = await WebhookFactory.createCryptoReceived(pdata.data, 'stellar');
            return await this.processReceivedWebhook(webhookPayload);
        }
        return this.makeResponse(200, "Event not processed");
    }

    // General webhook for external systems
    async webhooks(data: any) {
        const webhookPayload = WebhookFactory.createGeneralStatus(data);
        return await this.processReceivedWebhook(webhookPayload);
    }


    async banks(currency: any) {
        if (currency == '') {
            return await this.callQuery(`select * from banks`)
        }
        return await this.callQuery(`select * from banks where currency='${currency}' and status='ACTIVE'`)
    }
    async getTransactions(id: string) {
        const resp = await this.callQuery(`select * from quotes s inner join providers p on s.provider_id = p.provider_id INNER JOIN services r on r.service_id= s.service_id and s.company_id = '${id}' order by s.id DESC`)
        console.log(`resp====>`, resp)
        return this.makeResponse(200, "success", resp)
    }


    async getSingleTransaction(transId: any) {
        try {
            const rsp: any = await this.callQuery(`select * from quotes s inner join providers p on s.provider_id = p.provider_id INNER JOIN services r on r.service_id= s.service_id where transId='${transId}'`)
            if (rsp.length > 0) {
                const resp_format = {
                    thirdparty_trans_id: rsp[0].id,
                    amount_to_pay: rsp[0].receive_amount,
                    amount_paid: rsp[0].receive_amount,
                    currency: rsp[0].currency,
                    fee: rsp[0].fee,
                    ext_trans_id: rsp[0].transId,
                    account_number: rsp[0].account_number,
                    status: rsp[0].account_number,
                    quote_id: transId,
                    date: rsp[0].created_on,
                    channel: "MTN"

                };
                return this.makeResponse(200, "success", resp_format);
            } else {
                return this.makeResponse(404, "not found");


            }
        } catch (error) {
            console.error("Error in updateService:", error);
            return this.makeResponse(203, "Error");
        }
    }

    async getTransactionByRefTransId(transId: any) {
        const rsp: any = await this.callQuery(`select * from quotes  where client_reference_id='${transId}'`)
        if (rsp.length > 0) {
            return this.makeResponse(200, "success", rsp[0]);
        } else {
            return this.makeResponse(203, "Error getting transaction");


        }    }

    async getTransaction(transId: any) {
        try {
            const rsp: any = await this.callQuery(`select * from quotes s inner join providers p on s.provider_id = p.provider_id INNER JOIN services r on r.service_id= s.service_id where transId='${transId}'`)

            if (rsp.length > 0) {
                return this.makeResponse(200, "success", rsp[0]);
            } else {
                return this.makeResponse(203, "Error getting transaction");


            }
        } catch (error) {
            console.error("Error in updateService:", error);
            return this.makeResponse(203, "Error");
        }
    }


    async bookRate(data: RateRequest) {
        const qtId = "md" + this.getRandomString();
        const rate = await this.getRate(data, qtId);
        return rate
    }

    async createKotaniOffRamp(quoteId: string) {
        const quote: any = await this.selectDataQuery(`quotes`, `transId = '${quoteId}' and status = 'PENDING'`);
        console.log(`quote`, quote)
        if (quote.length == 0) {
            // return false;
        }
        const quoteData: Quote = quote[0];
        const { send_asset, receive_currency, send_amount, chain, payable_amount, sending_address } = quoteData;
        const paymentMethod = await this.getPaymentMethodById(quoteData.payment_method_id || "");
        if (paymentMethod.length == 0) {
            return false;
        }
        const paymentMethodData: PaymentMethod = paymentMethod[0];

        const { phone_number, network, bank_code, account_number, type, account_name } = paymentMethodData;
        const bankCode = bank_code;
        const address = "Kampala Uganda"

        const MMObject: any = {
            phoneNumber: account_number,
            networkProvider: network || "",
            currency: receive_currency.toLowerCase(),
            chain: chain,
            accountName: account_name,
            token: send_asset,
            cryptoAmount: Number(send_amount),
            referenceId: quoteId,
            senderAddress: quoteData.sending_address
        }
        const BankObject: any = {
            accountName: account_name,
            accountNumber: account_number,
            address: address,
            phoneNumber: phone_number,
            bankCode: bankCode,
            chain: chain,
            token: send_asset,
            cryptoAmount: Number(send_amount),
            referenceId: quoteId,
            senderAddress: quoteData.sending_address
        }
        let kotaniOffRamp: any = null;
        if (type == "mobile_money") {
            this.LogOperation(quoteId, 'kotani', 'KOTANI_OFFRAMP_REQUEST_MM', MMObject)
            kotaniOffRamp = await kotaniPay.createMobileMoneyOfframp(MMObject);
            this.LogOperation(quoteId, 'kotani', 'KOTANI_OFFRAMP_RESPONSE_MM', kotaniOffRamp)
        } else {
            this.LogOperation(quoteId, 'kotani', 'KOTANI_OFFRAMP_REQUEST_BANK', BankObject)
            kotaniOffRamp = await kotaniPay.createBankOfframp(BankObject);
            this.LogOperation(quoteId, 'kotani', 'KOTANI_OFFRAMP_RESPONSE_BANK', kotaniOffRamp)
        }
        this.LogOperation(quoteId, 'kotani', 'KOTANI_OFFRAMP_RESPONSE', kotaniOffRamp)
        if (kotaniOffRamp.success) {
            const escrowAddress = kotaniOffRamp.data.escrowAddress;
            return escrowAddress;
        }
        return null;
    }

    async getRate(data: RateRequest, quoteId: string = "") {
        try {
            console.log(`getRate`, data)
            const { amount, currency } = data
            let provider_id = data.provider_id || null
            let service_code = data.service_code || ""
            let service_id = data.service_code || ""
            let transaction_type = data.transaction_type || "off_ramp"

            // restrict on ramp support to off_ramp and on_ramp              
            const supportedCurrencyRamps = ["off_ramp", "on_ramp"]
            if (!supportedCurrencyRamps.includes(transaction_type)) {
                return this.makeResponse(203, "Not supported transaction type");
            }

            if (provider_id == null) {
                const provider: any = await this.searchprovider(service_code, currency)
                if (provider.length == 0) {
                    return this.makeResponse(203, "No provider found");
                }
                provider_id = provider[0].provider_id
            }

            if (service_id != "") {
                const service = await this.getService(service_id)
                console.log(`servicesInformato`, service)
                if (service.status == 200) {
                    const serviceData = service.data
                    console.log(`serviceData`, serviceData)
                    service_id = serviceData.service_id


                } else {
                    return this.makeResponse(203, "Service not found");
                }
            }


            const getprovider: any = await this.validateProviderCurrency(String(provider_id), String(currency));
            if (getprovider.length == 0) {
                return this.makeResponse(203, "Provider does not support this currency");
            }




            let symbol = data.asset_code || ""
            const chain = await this.getchain(symbol)
            const assetCode = await this.getAssetCode(symbol)
            if (!assetCode) {
                return this.makeResponse(203, "Asset code not supported");
            }
            const supportedCurrency = await this.getAssetCode(currency)
            if (!supportedCurrency) {
                return this.makeResponse(203, "Currency not supported");
            }




            const { mudaFee } = await this.calculateMudaFee(chain, amount)



            // Declare helper variables
            let healthResponse: RateResponse | any;
            let provider_quote_id: string = "";

            const usdStableAssets = ["USDC", "USDT"]
            const ngnStableAssets = ["cNGN"]

            // restrict on ramp to KES 
            if (transaction_type === "on_ramp" && currency != "KES") {
                return this.makeResponse(203, "No provider found for this currency on ramp");
            }

            if (provider_id == 2) {

                healthResponse = await kotaniPay.getOffRampRate(assetCode, currency, Number(amount));
                console.log(`healthResponseKotani`, healthResponse)
                provider_quote_id = healthResponse?.id || "";
            }else if (provider_id == 6) {
              
                const from_ = (transaction_type === "off_ramp")? assetCode : currency 
                const to_ = (transaction_type === "off_ramp")? currency : assetCode
                // assetCode, currency
                healthResponse = await HoneyCoin.getOffRampRate(from_, to_, Number(amount));
                healthResponse =  healthResponse?.data ?? healthResponse
                provider_quote_id = healthResponse?.id || "";
                console.log(`healthResponseHoneyCoin`, healthResponse)

            } else if (provider_id == 4) {
                
                healthResponse = await this.getQuidaxQuote(assetCode, currency, Number(amount));
                console.log(`healthResponseQuidax`, healthResponse)
                provider_quote_id = healthResponse?.id || "";
            } else if (provider_id == 3) {
                healthResponse = await Ogateway.getRates({
                    amount,
                    symbol: "USD",
                    currency,
                });
            } else if (provider_id == 1) {
                const supportedCurrency = ["UGX", "KES"]
                if (!supportedCurrency.includes(currency)) {
                    return this.makeResponse(203, "No provider found for this currency");
                }
                // Fallback to conversion rate
                const rate: any = await this.getConversionRate(amount, assetCode, currency, String(provider_id));
                console.log(`healthResponseConversionRate`, rate)

                const postRateRaw: number = typeof rate === 'string' ? parseFloat(rate) : Number(rate);
                const postRate: number = +postRateRaw.toFixed(2);
                //  const fee: number = +(0.01 * postRate).toFixed(2);
                const fee = 0;
                const totalAmount: number = +(postRate + fee).toFixed(2);
                const rateAmount = (totalAmount / parseFloat(String(amount))).toFixed(2)
                console.log(`rateAmount`, rateAmount)
                const expiresAt = new Date(Date.now() + 1000 * 60 * 30) // 10 minutes from now
                    .toISOString()
                    .slice(0, 19)
                    .replace('T', ' ');
                console.log(`expiresAt`, expiresAt)

                healthResponse = {
                    provider: "muda",
                    providerQuoteId: this.getRandomString(),
                    from: symbol,
                    providerId: provider_id,
                    to: currency,
                    fiatAmount: totalAmount,
                    toAmount: postRate,
                    cryptoAmount: amount,
                    fee,
                    quoteId: quoteId || "",
                    expiresAt: expiresAt,
                    quotedPrice: rateAmount
                } as RateResponse;
            } else {
                return this.makeResponse(203, "No provider found for this currency");
            }
            if (healthResponse.providerQuoteId && healthResponse.providerQuoteId != "") {
                healthResponse = healthResponse
            } else {
                return this.makeResponse(203, healthResponse.message, healthResponse);
            }

            // Attach quoteId to response object
            const fee = healthResponse.fee + mudaFee
            healthResponse.fee = fee
            if (quoteId !== "") {
                const saveToDb = healthResponse;
                saveToDb.quoteId = quoteId;
                saveToDb.asset_code = symbol;
                saveToDb.currency = currency;
                saveToDb.service_id = service_id;
                delete saveToDb.from;
                delete saveToDb.to;
                saveToDb.transactionType = transaction_type;
                await this.insertData("quote_log", saveToDb);
            }
            delete healthResponse.providerQuoteId;
            delete healthResponse.symbol;
            healthResponse.asset_code = symbol;


            console.log(`healthResponseFinal`, healthResponse);
            return this.makeResponse(200, "success", healthResponse);
        } catch (error) {
            console.error("Error in updateService:", error);
            return this.makeResponse(203, "error");
        }
    }

    async getService(service_id: string) {
        const resp: any = await this.callQuery(`select * from services where service_id='${service_id}' or service_code='${service_id}'`)
        if (resp.length > 0) {
            return this.makeResponse(200, "success", resp[0]);
        } else {
            return this.makeResponse(404, "Service not found");
        }
    }

    async services() {
        const response: any = await this.callQuery(`select * from services`)
        return this.makeResponse(200, "success", response);
    }


    async getAssetCode(code: string) {
        const asset: any = await this.callQuery(`select asset_name from accepted_assets where asset_code='${code}'`)
        if (asset.length > 0) {
            return asset[0].asset_name
        }
        return false;
    }

    async getProviders(data: RateRequest, filter = "all") {
        console.log(`provider==>1`, data)
        const service = data.service_code || ""
        const currency = data.currency;
        const amount = data.amount || 1
        const transaction_type = data.transaction_type || "off_ramp"
        const asset = await this.getAssetCode(data.asset_code || "")

        try {
            let provider: any = await this.callQuery(
                `SELECT * FROM service_providers s 
             INNER JOIN providers p ON s.provider_id = p.provider_id 
             INNER JOIN services r ON r.service_id = s.service_id 
             WHERE currency='${currency}'  ${service ? `and (r.service_code='${service}' OR s.service_id='${service}')  ` : ''}`
            );
            console.log(`PROVIDERS==>1`, provider)

            const providers: ProviderService[] = [];
            for (let i = 0; i < provider.length; i++) {
                try {
                    const provider_id = provider[i].provider_id;
                    const rateReq: RateRequest = {
                        // asset_code: asset,
                        // currency: currency,
                        asset_code: asset,
                        currency: currency,
                        amount,
                        provider_id: provider_id,
                        service_code: service,
                        transaction_type: transaction_type
                    };

                    let exRate = 0;
                    let fiatAmount = 0;
                    let fee = 0;

                    const rate: any = await this.getRate(rateReq);
                    console.log(`PROVIDERS==>2`, rate)
                    if (rate.data.fiatAmount) {
                        fiatAmount = rate.data.fiatAmount;
                        fee = rate.data.fee
                        exRate = rate.data.quotedPrice
                    }

                    provider[i].fiatAmount = fiatAmount;
                    provider[i].rate = exRate
                    provider[i].fee = fee;
                    provider[i].transaction_type = transaction_type;

                    if (exRate > 0) {
                        providers.push(provider[i]);
                    }
                } catch (error) {
                    console.log(`PROVIDERS==>3`, error)
                    provider[i].rate = 0;
                }
            }

            // 🔥 Sort providers by rate in DESC order before returning
            const sortedProviders = providers.sort((a, b) => b.rate - a.rate);
            console.log(`sortedProviders`, sortedProviders)
            if (sortedProviders.length > 0) {

                if (filter == "all") {
                    return this.makeResponse(200, "success", sortedProviders);
                } else {
                    return this.makeResponse(200, "success", sortedProviders[0]);
                }

            } else {
                return this.makeResponse(404, "No providers found");
            }



        } catch (error) {
            console.error("Error in updateService:", error);
            return this.makeResponse(500, "Error getting providers");
        }
    }


    async assets() {
        return await this.selectDataQuery("assets")

    }
    async currencies() {
        return await this.selectDataQuery("currencies")
    }

    // Delete a service
    async deleteAddress(service_id: string) {
        try {
            await this.deleteData('addresses', `id = '${service_id}'`);
            return this.makeResponse(200, "Service deleted successfully");
        } catch (error) {
            console.error("Error in deleteService:", error);
            return this.makeResponse(203, "Error deleting service");
        }
    }

    // Delete a service
    async deletePhoneNumber(service_id: any, userId: string) {
        try {
            console.log(`deletePhoneNumber`, service_id, userId)
            const method = await this.selectDataQuery("payment_methods", `payment_method_id = '${service_id}' and company_id='${userId}'`);
            if (method.length == 0) {
                return this.makeResponse(203, "Item not found");
            }
            await this.deleteData('payment_methods', `payment_method_id = '${service_id}' and company_id='${userId}'`);

            return this.makeResponse(200, "Item deleted successfully");
        } catch (error) {
            console.error("Error in deleteService:", error);
            return this.makeResponse(203, "Error deleting service");
        }
    }

    // Delete a service
    async deleteService(service_id: number) {
        try {
            await this.deleteData('services', `service_id = '${service_id}'`);
            return this.makeResponse(200, "Service deleted successfully");
        } catch (error) {
            console.error("Error in deleteService:", error);
            return this.makeResponse(203, "Error deleting service");
        }
    }

    async getPaymentMethodAccountNumber(payId: string) {
        return await this.selectDataQuery("payment_methods", `account_number = '${payId}'`);
    }

    async getPaymentMethodAccount(payId: string, company_id: string) {
        return await this.selectDataQuery("payment_methods", `company_id='${company_id}' and phone_number = '${payId}'`);
    }

    async getPaymentMethods(company_id: string) {
        return await this.selectDataQuery("payment_methods", `company_id = '${company_id}'`);
    }

    async getPaymentMethodForCurrency(company_id: string, currency: string) {
        return await this.selectDataQuery("payment_methods", `company_id = '${company_id}' and currency='${currency}'`);
    }



    async getAddressesForCurrency(company_id: string, currency: string, chain: string) {
        //  const chain = await this.getchain(currency)
        return await this.selectDataQuery("addresses", `company_id = '${company_id}' and chain='${chain}'`);
    }


    async addPaymentMethod(data: any) {
        try {
            const { company_id, currency, type } = data;

            if (!company_id || !type) {
                //   return this.makeResponse(400, "Company ID and payment method type are required");
            }

            if (type !== "bank" && type !== "mobile_money") {
                return this.makeResponse(400, "Invalid payment method type");
            }
            let PaymentMethodId = this.getRandomString()

            let newPaymentMethod: BankPayment | MobileMoneyPayment;
            let kotani_customer_key = ""
            const network = "MTN"
            if (type === "mobile_money") {
                PaymentMethodId = "1" + PaymentMethodId
                const country_code = "UG"

                const { phone_number, account_name } = data as MobileMoneyPayment;
                if (phone_number.length < 11) {
                    return this.makeResponse(400, "Phone number is not valid, must start with a country code");
                }
                const paymethod = await this.getPaymentMethodAccount(phone_number, company_id)
                if (paymethod.length > 0) {
                    return this.makeResponse(400, "Phone number exists");
                }

                const addResponse = await kotaniPay.createCustomerMobileMoney(phone_number, country_code, network, account_name);
                console.log(`addResponse`, addResponse)
                const status = addResponse.success
                if (status) {
                    kotani_customer_key = addResponse.data.customer_key
                }

                if (!phone_number || !country_code) {
                    return this.makeResponse(400, "Phone number and country code are required for mobile money");
                }
                newPaymentMethod = { kotani_customer_key, currency, company_id, type, phone_number, country_code, network, account_name };
            } else {
                PaymentMethodId = "2" + PaymentMethodId
                const { bank_name, bank_code, account_number, bank_address, bank_phone_number, bank_country, account_name } = data as BankPayment;
                if (!bank_name || !account_number || !bank_code) {
                    return this.makeResponse(400, "Bank name, account number, and bank code are required for bank payment method");
                }
                newPaymentMethod = { phone_number: account_number, kotani_customer_key, currency, company_id, type, bank_name, bank_code, account_number, bank_address, bank_phone_number, bank_country, account_name };
            }


            let addPaymentMethod: any = newPaymentMethod
            addPaymentMethod.payment_method_id = PaymentMethodId

            const insertedPaymentMethod = await this.insertData("payment_methods", addPaymentMethod);
            return this.makeResponse(200, "Payment method added successfully", addPaymentMethod);
        } catch (error) {
            console.error("Error in addPaymentMethod:", error);
            return this.makeResponse(500, "Error adding payment method");
        }
    }


    async getChains() {
        const data: any = await this.callQuery(`select * from chains `)
        return this.makeResponse(203, "sucess", data);
    }
    // Create a new address for a company
    async addAddress(data: any) {
        try {

            console.log(`data`, data)
            const { company_id, wallet_name, address, chain } = data;
            const chains = ["STELLAR", "TRON", "CELO", "BANTU", "BSC"]

            if (!chains.includes(chain)) {
                return this.makeResponse(203, "chain not supported");
            }
            const addExists: any = await this.callQuery(`select * from addresses where address='${address}' and company_id='${company_id}' `)
            if (addExists.length > 0) {
                return this.makeResponse(203, "Address exists already");

            }


            //  console.log(``)
            // add a check for a valid address
            if (chain === "BANTU" && (!address.startsWith('G') || address.length !== 56)) {
                return this.makeResponse(203, "Invalid BANTU address");
            }

            if (chain === "STELLAR" && (!address.startsWith('G') || address.length !== 56)) {
                return this.makeResponse(203, "Invalid XLM address");
            }

            if (chain === "TRON" && (!address.startsWith('T') || address.length !== 34)) {
                return this.makeResponse(203, "Invalid TRON address");
            }
            if (chain === "CELO" && (!address.startsWith('0x') || address.length !== 42)) {
                return this.makeResponse(203, "Invalid CELO address");
            }
            const newAddress = { company_id, wallet_name, address, chain, created_at: new Date() };
            const insertedAddress = await this.insertData('addresses', newAddress);
            return this.makeResponse(200, "Address added successfully", insertedAddress);
        } catch (error) {
            console.error("Error in addAddress:", error);
            return this.makeResponse(203, "Address already exists");
        }
    }



    async getQuotestatus() {
        const quote: any = await this.callQuery(`
            SELECT * 
            FROM quotes 
            WHERE LOWER(status) NOT IN ('successful', 'failed', 'expired', 'cancelled')
          `);
        console.log(`OBTAINED_QUOTED`, quote.length)
        for (let i = 0; i < quote.length; i++) {
            try {
                const created_on = quote[i].created_on
                const pay_in_status = quote[i].pay_in_status
                const provider_id = quote[i].provider_id
                const transId = quote[i].transId

                if (provider_id == 2) { //for kotani
                    const healthResponse = await kotaniPay.getOfframpStatus(transId);
                    console.log(`getQuotestatus`, healthResponse)
                    const status = healthResponse.success
                    if (status) {
                        const fiatStatus = healthResponse.data.status
                        const onchainStatus = healthResponse.data.onchainStatus

                        const updates: any = {
                            pay_in_status: onchainStatus,
                            status: fiatStatus
                        };
                        await this.updateData('quotes', `transId = '${transId}'`, updates);
                    } else {
                        // await this.checkExpired(transId, created_on)
                    }

                } else {
                    // await this.checkExpired(transId, created_on)

                }
            } catch (error) {
                console.log(`Kotanierror`, error)

            }
        }

    }

    async expirePendingQuotes() {
        try {
            const pendingQuotes: any = await this.callQuery(`
                SELECT * FROM quotes 
                WHERE status = 'PENDING' AND pay_in_status = 'PENDING'
            `);

            const currentDate = new Date();

            for (let quote of pendingQuotes) {
                const createdOnDate = new Date(quote.created_on);
                const expiresAt = new Date(quote.expires_at);
                console.log(`createdOnDate`, createdOnDate, expiresAt)

                const timeSinceCreationInMinutes = (currentDate.getTime() - createdOnDate.getTime()) / (1000 * 60);
                console.log(`timeSinceCreationInMinutes`, timeSinceCreationInMinutes)
                if (currentDate > expiresAt && timeSinceCreationInMinutes >= 120) {
                    const updates = {
                        status: 'EXPIRED',
                        narration: 'EXPIRED'
                    };
                    await this.updateData('quotes', `transId = '${quote.transId}'`, updates);
                    await WebhookSender.send(quote.transId);
                }
            }

            return this.makeResponse(200, "Pending quotes expired successfully");
        } catch (error) {
            console.error("Error in expirePendingQuotes:", error);
            return this.makeResponse(203, "Error expiring pending quotes");
        }
    }



    // Fetch addresses for a specific company
    async getAddresses(company_id: string) {
        try {
            const addresses = await this.selectDataQuery("addresses", `company_id = '${company_id}'`);
            if (addresses.length > 0) {
                return this.makeResponse(200, "Addresses fetched successfully", addresses);
            } else {
                return this.makeResponse(404, "Addresses not found for the given company");
            }
        } catch (error) {
            console.error("Error in getAddresses:", error);
            return this.makeResponse(203, "Error fetching addresses");
        }
    }

    async getAllQuotes(wallet: any, data: any): Promise<any> {
        // Remove all keys with empty values from data
        const cleanData = Object.entries(data || {}).reduce((acc, [key, value]) => {
            if (value !== null && value !== undefined && value !== '') {
                if (Array.isArray(value)) {
                    if (value.length > 0 && value.some(v => v !== null && v !== undefined && v !== '')) {
                        acc[key] = value.filter(v => v !== null && v !== undefined && v !== '');
                    }
                } else {
                    acc[key] = value;
                }
            }
            return acc;
        }, {} as any);

        const PAGE: number = Number(cleanData?.page) || 1;
        const LIMIT = cleanData?.limit || 14;
        const OFFSET = (PAGE - 1) * LIMIT;
        const SEARCH = cleanData?.search;
        const CURRENCY = cleanData?.currency;
        const PAY_IN_STATUS = cleanData?.pay_in_status ? (Array.isArray(cleanData.pay_in_status) ? cleanData.pay_in_status : [cleanData.pay_in_status]) : ['SUCCESSFUL', 'PENDING'];
        const STATUS = cleanData?.status || [];
        const PROVIDER_ID = cleanData?.provider_id || [];
        const SEND_ASSET = cleanData?.send_asset || [];
        const RECEIVE_CURRENCY = cleanData?.receive_currency || [];
        const START_DATE = cleanData?.start_date || '';
        const END_DATE = cleanData?.end_date || '';
        const SORT_KEY = cleanData?.sort_key || '';
        const SORT_ORDER = cleanData?.sort_order || 'DESC';

        let whereClause = '';
        const conditions = [];

        conditions.push(`(
            p.provider_address = '${wallet}' OR 
            p.sending_address = '${wallet}' OR
            p.receiver_address = '${wallet}'
        )`);

        if (SEARCH) {
            conditions.push(`(
            p.transId LIKE '%${SEARCH}%' OR 
            p.provider_address LIKE '%${SEARCH}%' OR
            p.account_number LIKE '%${SEARCH}%' OR
            p.hash LIKE '%${SEARCH}%' OR
            p.company_id LIKE '%${SEARCH}%' OR
            p.sending_address LIKE '%${SEARCH}%' OR 
            p.receiver_address LIKE '%${SEARCH}%'
        )`);
        }

        // Always include pay_in_status filter for SUCCESSFUL or PENDING by default
        if (Array.isArray(PAY_IN_STATUS) && PAY_IN_STATUS.length > 0) {
            const statusConditions = PAY_IN_STATUS.map(status => `p.pay_in_status = '${status}'`).join(' OR ');
            conditions.push(`(${statusConditions})`);
        } else if (PAY_IN_STATUS) {
            conditions.push(`p.pay_in_status = '${PAY_IN_STATUS}'`);
        } else {
            // Default to SUCCESSFUL and PENDING if no pay_in_status provided
            conditions.push(`(p.pay_in_status = 'SUCCESSFUL' OR p.pay_in_status = 'PENDING')`);
        }

        if (START_DATE && START_DATE !== '' && START_DATE !== null && START_DATE !== undefined) {
            conditions.push(`DATE(p.created_on) >= '${START_DATE}'`);
        }

        if (END_DATE && END_DATE !== '' && END_DATE !== null) {
            conditions.push(`DATE(p.created_on) <= '${END_DATE}'`);
        }

        if (Array.isArray(SEND_ASSET) && SEND_ASSET.length > 0) {
            const sendAssetConditions = SEND_ASSET.map(asset => `p.send_asset = '${asset}'`).join(' OR ');
            conditions.push(`(${sendAssetConditions})`);

        } else if (SEND_ASSET && SEND_ASSET !== '' && SEND_ASSET.length > 0) {
            conditions.push(`p.send_asset = '${SEND_ASSET}'`);
        }

        if (Array.isArray(RECEIVE_CURRENCY) && RECEIVE_CURRENCY.length > 0) {
            const receiveCurrencyConditions = RECEIVE_CURRENCY.map(currency => `p.receive_currency = '${currency}'`).join(' OR ');
            conditions.push(`(${receiveCurrencyConditions})`);

        } else if (RECEIVE_CURRENCY && RECEIVE_CURRENCY !== '' && RECEIVE_CURRENCY.length > 0) {
            conditions.push(`p.receive_currency = '${RECEIVE_CURRENCY}'`);
        }

        if (Array.isArray(STATUS) && STATUS.length > 0) {
            const statusConditions = STATUS.map(status => `p.status = '${status}'`).join(' OR ');
            conditions.push(`(${statusConditions})`);

        } else if (STATUS && STATUS !== '' && STATUS.length > 0) {
            conditions.push(`p.status = '${STATUS}'`);
        }

        if (Array.isArray(PROVIDER_ID) && PROVIDER_ID.length > 0) {
            const providerConditions = PROVIDER_ID.map(id => `p.provider_id = '${id}'`).join(' OR ');
            conditions.push(`(${providerConditions})`);
        } else if (PROVIDER_ID && PROVIDER_ID !== '' && PROVIDER_ID.length > 0) {

            conditions.push(`p.provider_id = '${PROVIDER_ID}'`);
        }

        if (conditions.length > 0) {
            whereClause = `WHERE ${conditions.join(' AND ')}`;
        }

        let orderClause = '';
        if (SORT_KEY) {
            const validSortKeys = ['created_at', 'transId', 'send_asset', 'receive_currency', 'pay_in_status', 'status'];
            const validSortOrders = ['ASC', 'DESC'];

            if (validSortKeys.includes(SORT_KEY)) {
                const order = validSortOrders.includes(SORT_ORDER.toUpperCase()) ? SORT_ORDER.toUpperCase() : 'DESC';
                orderClause = `ORDER BY p.${SORT_KEY} ${order}`;
            }
        } else {
            orderClause = 'ORDER BY p.created_ON DESC';
        }

        const query = `
        SELECT
            p.*,
            p.id AS quote_id,
            an.*,
            an.id AS payment_mtd_id,
            sp.*, 
            serv.*
        FROM quotes p 
        LEFT JOIN payment_methods an ON p.payment_method_id = an.payment_method_id
        LEFT JOIN service_providers sp ON p.provider_id = sp.provider_service_id
        LEFT JOIN services serv ON p.service_id = serv.service_id
        ${whereClause}
        ${orderClause}
        LIMIT ${LIMIT} OFFSET ${OFFSET}
        `;
        const countQuery = `
        SELECT COUNT(*) as total 
        FROM quotes p 
        LEFT JOIN payment_methods an ON p.payment_method_id = an.payment_method_id
        LEFT JOIN service_providers sp ON p.provider_id = sp.provider_service_id
        LEFT JOIN services serv ON p.service_id = serv.service_id
        ${whereClause}
        `;

        const [items, countResult] = await Promise.all([
            this.callRawQuery(query),
            this.callRawQuery(countQuery)
        ]);

        const total = countResult[0]?.total || 0;
        const totalPages = Math.ceil(total / LIMIT);

        const groupedItems = items.map((item: any) => {
            const {
                quote_id, payment_method_id, kotani_customer_key, type, currency, phone_number,
                country_code, network, account_name, bank_name, bank_code,
                account_number, bank_address, bank_phone_number, bank_country,
                sort_code, swift_code, created_at: payment_created_at,
                updated_at: payment_updated_at,
                provider_service_id, service_id, provider_id, min_amount, max_amount,
                service_code, service_name, country, provider_type,
                ...quoteData
            } = item;

            return {
                ...quoteData,
                id: quote_id,
                payment_method: {
                    id: payment_method_id,
                    kotani_customer_key,
                    type,
                    currency,
                    phone_number,
                    country_code,
                    network,
                    account_name,
                    bank_name,
                    bank_code,
                    account_number,
                    bank_address,
                    bank_phone_number,
                    bank_country,
                    sort_code,
                    swift_code,
                    created_at: payment_created_at,
                    updated_at: payment_updated_at
                },
                service_provider: {
                    provider_service_id,
                    service_id,
                    provider_id,
                    min_amount,
                    max_amount,
                    service: {
                        service_code,
                        service_name,
                        country,
                        provider_type
                    }
                }
            };
        });

        const reports = {
            items: groupedItems,
            pagination: {
                current_page: PAGE,
                next_page: PAGE < totalPages ? PAGE + 1 : null,
                previous_page: PAGE > 1 ? PAGE - 1 : null,
                total_pages: totalPages,
                total_items: total,
                items_per_page: LIMIT
            }
        };

        return this.makeResponse(200, "Transactions reports fetched successfully", reports);
    }

    /* --------------------------------------------------------------------
       MUDA FEE HELPERS
     ------------------------------------------------------------------ */

    /**
     * Calculate the internal MUDA fee for a quote.
     */
    private async calculateMudaFee(chain: string, amount: any): Promise<any> {
        try {
            const send_amount = parseFloat(amount)
            const fees: any = await this.callQuery(`SELECT * FROM lr_fees inner join chains c on lr_fees.chain_id = c.chain_id where c.chain_code='${chain}'`);

            if (fees.length == 0) {
                return { blockchainFee: 0, mudaFee: 0, totalFees: 0, payableAmount: amount }
            }
            console.log(`fees::1`, fees)
            const fee_type = fees[0].charge_type
            const block_fee_usdt = fees[0].gas_fee
            let mudaFee: any = 0
            if (fee_type == "percentage") {
                const fee_value = fees[0].muda_charge
                mudaFee = (send_amount * fee_value) / 100
            } else {
                mudaFee = fees[0].muda_charge
            }
            const miniFee = 0
            if (mudaFee < miniFee) {
                mudaFee = miniFee
            }
            const blockchainFee = parseFloat(block_fee_usdt)
            const totalFees = parseFloat(mudaFee) + blockchainFee
            const payableAmount = send_amount - totalFees
            console.log(`send_amount`, send_amount)
            console.log(`totalFees`, totalFees)
            console.log(`payableAmount`, payableAmount)
            console.log(`blockchainFees::3`, blockchainFee, mudaFee, totalFees, payableAmount)

            return { blockchainFee, mudaFee, totalFees, payableAmount }
        } catch (err) {
            console.log(`err::1`, err)
            return { blockchainFee: 0, mudaFee: 0, totalFees: 0, payableAmount: amount }
        }
    }

    /**
     * Persist a fee log for later reconciliation / analytics.
     * Columns expected (based on previous schema):
     *   quote_id | provider_service_id | muda_fee | thirdparty_fee | thirdparty_quote | thirdparty_rate |
     *   blockchain_fee | blockchain_fee_asset | rate
     */
    private async logMudaFee(params: {
        quote_id: string;
        provider_service_id: number | string;
        muda_fee: number;
        thirdparty_fee?: number;
        thirdparty_quote?: string;
        thirdparty_rate?: number | string;
        blockchain_fee?: number;
        blockchain_fee_asset?: string;
        rate?: number | string;
    }): Promise<void> {
        try {
            // Table assumed to be `muda_fee_log`. Change if your actual table differs.
            await this.insertData('muda_fee_log', params);
        } catch (err) {
            console.error('Failed to write fee log:', err);
        }
    }

}

export default Accounts;
