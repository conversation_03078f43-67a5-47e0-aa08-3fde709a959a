import express, { Request, Response } from 'express';
import bodyParser from 'body-parser';
import cors from 'cors';
import dotenv from 'dotenv';
import expressFileUpload from 'express-fileupload';

import accounts from './controllers/accounts';
import zoho from './controllers/zoho';
import admin from './controllers/admin';
import {runTests} from './tests/tests'
import CronService from './helpers/cron';
import payments from './controllers/payments';

dotenv.config();

const app = express();
const PORT = process.env.PORT || 3000; // Default to 3000 if PORT is not in environment

app.use(cors());
app.use(expressFileUpload()); // Use express-fileupload before body-parser
app.use(bodyParser.json());


// Using the routes
app.use('/accounts', accounts);
app.use('/users', accounts);
app.use('/zoho', zoho);
app.use('/admin', admin);
app.use('/payments', payments);

app.use('/health', (req: Request, res: Response) => {
  res.status(200).json({ status: 'ok', service: 'liquidity-rail-admin' });
});

runTests()
new CronService()

app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
});
