import axios from "axios";

class Ogateway {
  private mainURL: string;
  private mainRequestHeader: any;

  constructor() {
    this.mainURL = "https://api.ogateway.io/";
    this.mainRequestHeader = { 
      Accept: "application/json",
      "Content-Type": "application/json",
      Authorization: `${process.env.OGATEWAY_TEST}`,
    };
    console.log(`mainRequestHeader`, this.mainRequestHeader)
  }

  // **Reusable POST Request Method**
  private async postRequest(endpoint: string, data: any, headers = this.mainRequestHeader) {
    console.log(`[POST REQUEST] - ${this.mainURL}${endpoint}`, { data, headers });

    try {
      const response = await axios.post(`${this.mainURL}${endpoint}`, data, { headers });
      console.log(`[POST RESPONSE] - ${this.mainURL}${endpoint}`, response.data);
      return this.handleResponse(response);
    } catch (error) {
      return this.handleError(error, "POST", endpoint);
    }
  }

  // **Reusable GET Request Method**
  private async getRequest(endpoint: string, headers = this.mainRequestHeader) {
    console.log(`[GET REQUEST] - ${this.mainURL}${endpoint}`, { headers });

    try {
      const response = await axios.get(`${this.mainURL}${endpoint}`, { headers });
      console.log(`[GET RESPONSE] - ${this.mainURL}${endpoint}`, response.data);
      return this.handleResponse(response);
    } catch (error) {
      return this.handleError(error, "GET", endpoint);
    }
  }

  // **Handle API Responses**
  private handleResponse(response: any) {
    if (response.data?.status === "error") {
      console.error(`[API ERROR] - Response error:`, response.data);
      return { error: response.data };
    }
    return response.data;
  }

  // **Handle API Errors**
  private handleError(error: any, method: string, endpoint: string) {
    console.error(`[${method} ERROR] - ${this.mainURL}${endpoint}`, error.response?.data || error.message);
    return { error: error.response?.data || "An error occurred" };
  }

  // **Create a Sub-Account**
  public async createSubAccount(data: any) {
    return this.postRequest("users", data.body, {
      accept: "application/json",
      authorization: `Bearer ${data.token}`,
    });
  }

  // **Collect Mobile Money**
  public async sendToMobile(reference:string,amount: number,accountName:string,accountNumber:string) {
    const data = {
      recipients: [
        {
          network: "MTN",
          currency: "GHS",
          amount,
          accountName,
          accountNumber,
        }
      ],
      reference,
      callbackURL: process.env.CALLBACK_URL,
    };
    return this.postRequest("disbursements/mobilemoney", data);
  }
  

  // **Get Account Balance**
  public async getAccountBalance() {
    return this.getRequest("balance");
  }

  // **Query an Account**
  public async accountQuery(data: any) {
    return this.getRequest(
      `accountquery?currency=${data.currency}&accountCode=${data.accountCode}&accountNumber=${data.accountNumber}`
    );
  }

  // **Get Payment Methods**
  public async paymentMethods(data: any) {
    return this.getRequest(`payment-methods/${data.currency}`);
  }

  // **Get Exchange Rates**
  public async getRates(data: any) {
    return this.getRequest(`rates?source=${data.source}&destination=${data.destination}&amount=${data.amount}`);
  }

  // **Credit Withdrawal (Handles Mobile Money & Bank Transfers)**
  public async creditWithdraw(data: any = {}) {
    console.log(`[CREDIT WITHDRAW] - Type: ${data.type}`);
    return data.type === "mobile_money" ? this.withdrawToMobileMoney(data) : this.withdrawToBank(data);
  }

  private async withdrawToMobileMoney(data: any = {}) {
    return this.postRequest("disbursements/mobilemoney", data.body);
  }

  private async withdrawToBank(data: any = {}) {
    return this.postRequest("disbursements/bank", data.body);
  }

  // **Get Payments**
  public async getPayments(data: any) {
    return this.getRequest(
      `payments?status=${data.status}&type=${data.type}&channel=${data.channel}&currency=${data.currency}&cursor=${data.cursor}`
    );
  }

  // **Get Single Payment Details**
  public async getSinglePaymentDetails(data: any) {
    return this.getRequest(`payments/${data.id}`);
  }
  // **Convert Currency**
  public async convertCurrency(amount: number, source: string, destination: string) {
    return this.postRequest("converter", {
      amount,
      source,
      destination
    });
  }
}

export default new Ogateway();
