// kotani.ts

import { RateResponse } from "../interfaces/rate.interfaces";
import { get, post, patch } from "./kotaniHelper";
interface MobileMoney {
    providerNetwork: string;
    phoneNumber: string;
    accountName: string;
}

interface OnRampRequest {
    mobileMoney: MobileMoney;
    currency: string;
    chain: string;
    token: string;
    fiatAmount: number;
    receiverAddress: string;
    referenceId: string;
}

class KotaniPay {

    async createBankOfframp(data: any) {

        const { accountName, phoneNumber, address, bankCode, country, accountNumber, currency, chain, token, cryptoAmount, referenceId, senderAddress } = data;
        const payload = {
            "bankReceiver": {
                "name": accountName,
                "phoneNumber": phoneNumber,
                "address": address,
                "bankCode": bankCode,
                "country": country,
                "accountNumber": accountNumber
            },
            "currency": currency,
            "chain": chain,
            "token": token,
            "cryptoAmount": cryptoAmount,
            "referenceId": referenceId,
            "senderAddress": senderAddress
        };

        console.log(`KOTANIinfo`, payload)
        const response = await post("api/v3/offramp", payload);
        console.log(`KOTANIinfoResponse`, response)

        return response.data;
    }


    async kotaniOnRamp(requestData: OnRampRequest): Promise<any> {
        try {
            const onramp = {
                "mobileMoney": {
                    "providerNetwork": "MTN",
                    "phoneNumber": "+************",
                    "accountName": "MBONYE EMMANUEL"
                },
                "currency": "UGX",
                "chain": "STELLAR",
                "token": "USDC",
                "fiatAmount": 2000,
                "receiverAddress": "GDEJYFRWPZIA4NIJD7FFDNQ6UQHCM3AL6SINMHKUHZ2T5B5YJE2NW2CF",
                "referenceId": "muda-1234"
            }

            const response = await post(`api/v3/onramp`, onramp);
            return response.data;
        } catch (error) {
            console.error('Error making Kotani onramp request:', error);
            throw error;
        }
    }




    public async appHealth() {
        const response = await get("health");
        return response.data;
    }

    public async createInterator(data: any) {
        const response = await post("api/v3/integrator", data.body);
        return response.data;
    }

    public async getRate(from: string, to: string) {
        const response = await get(`api/v3/rate/${from}/${to}`);
        return response;
    }

    
    public async getOffRampRate(from: string, to: string, amount: number) {

        const req = {
            "from": from,
            "to": to,
            "cryptoAmount": amount
        }
        console.log(`KOTANIs`, req)
        const response = await post(`api/v3/rate/offramp`, req);
        console.log(`KOTANIsResponse`, response)
        const kotaniResponse = response;
        const status = kotaniResponse?.success;

        if (status) {
             const expiresAt = new Date(Date.now() + 1000 * 60 * 30) // 10 minutes from now
                .toISOString()
                .slice(0, 19)
                .replace('T', ' ');
            console.log(`expiresAt`, expiresAt)


            const quoteInfo: RateResponse = {
                provider: "kotani",
                providerQuoteId: kotaniResponse.data.id,
                from: from,
                providerId: 2,
                to: to,
                fiatAmount: kotaniResponse.data.fiatAmount,
                toAmount: kotaniResponse.data.transactionAmount,
                cryptoAmount: kotaniResponse.data.cryptoAmount,
                fee: kotaniResponse.data.fee,
                quoteId: kotaniResponse.data.id,
                expiresAt: expiresAt,
                quotedPrice: kotaniResponse.data.value
            };
            return quoteInfo;
        }else{
            return kotaniResponse
        }


    }



    public async transfer(walletId: string, amount: number) {
        const response = await post(`api/v3/wallet/transfer/deposit-balance`, { walletId, amount });
        return response.data;
    }




    public async getInterator() {
        const response = await get("api/v3/integrator");
        return response.data;
    }

    public async verifyAccount(data: any) {
        const response = await get(`api/v3/auth/verify?hash=${data.body.hash}`);
        return response.data;
    }

    public async login(email: any) {
        const response = await post("api/v3/auth/login", { email });
        return response.data;
    }

    public async authorizeUserDetails(data: any) {
        const response = await get(`api/v3/auth/session/${data.body.session_id}`);
        return response.data;
    }

    public async logoutUserDetails(data: any) {
        const response = await get(`api/v3/auth/logout/${data.body.session_id}`);
        return response.data;
    }

    public async getRefreshToken(data: any) {
        const response = await get("api/v3/auth/refresh-token");
        return response.data;
    }

    public async getApiKeyHash() {
        const response = await get("api/v3/auth/api-key");
        return response.data;
    }

    public async generateApiKey(data: any) {
        // In this case, we assume no body is needed.
        const response = await post("api/v2/auth/api-key", {});
        return response.data;
    }

    public async createWalletFiat(name: string, currency: string) {
        const response = await post("api/v3/wallet/fiat", { currency, name });
        return response.data;
    }

    public async getWalletFiats() {
        // If there are query parameters, pass them as the third argument.
        const response = await get("api/v3/wallet/fiat");
        return response.data;
    }

    public async getWalletFiatById(data: any) {
        const response = await get(`api/v3/wallet/fiat/${data.body.id}`);
        return response.data;
    }

    public async editWalletFiatById(data: any) {
        const response = await patch(`api/v3/wallet/fiat/${data.params.id}`, data.body);
        return response.data;
    }

    public async createCustomerMobileMoney(
        phoneNumber: string,
        countryCode: string,
        network?: string,
        accountName?: string
    ) {
        // Construct the payload with required and optional parameters
        const payload = {
            phone_number: phoneNumber,
            country_code: countryCode,
            ...(network && { network }),
            ...(accountName && { account_name: accountName }),
        };

        console.log(`payloadpayload`, payload)
        // return false;

        const response = await post("api/v3/customer/mobile-money", payload);
        return response.data;
    }


    public async getCustomerMobileMoney(data: any) {
        const response = await get("api/v3/customer/mobile-money");
        return response.data;
    }

    public async getCustomerMobileMoneyByCustomerKey(data: any) {
        const response = await get(`api/v3/customer/mobile-money/${data.params.customer_key}`);
        return response.data;
    }

    public async getCustomerMobileMoneyByPhoneNumber(data: any) {
        const response = await get(`api/v3/customer/mobile-money/phone/${data.params.phone_number}`);
        return response.data;
    }

    public async editCustomerMobileMoneyByCustomerKey(data: any) {
        const response = await patch(`api/v3/customer/mobile-money/${data.params.customer_key}`, data.body);
        return response.data;
    }

    public async createWithdrawMobileMoney(data: any) {
        const response = await post("api/v3/withdraw/mobile-money", data);
        return response.data;
    }

    public async getWithdrawMobileMoneyByReferenceId(data: any) {
        const response = await get(`api/v3/withdraw/status/${data.params.reference_id}`);
        return response.data;
    }

    public async createDepositMobileMoney(data: any) {
        const response = await post("api/v3/deposit/mobile-money", data);
        return response.data;
    }

    public async getDepositMobileMoneyByReferenceId(data: any) {
        const response = await get(`api/v3/deposit/mobile-money/status/${data.params.reference_id}`);
        return response.data;
    }

    public async verifyWithdraw(data: any) {
        console.log("verifyWithdraw", data);
    }

    public async verifyDeposit(data: any) {
        console.log("verifyDeposit", data);
    }

    //

    public async createOnrampCrypto(
        chain: string,
        token: string,
        cryptoAmount: number,
        referenceId: string,
        receiverAddress: string
    ) {
        const payload = {
            chain,
            token,
            crypto_amount: cryptoAmount,
            reference_id: referenceId,
            receiver_address: receiverAddress,
        };

        const response = await post("api/v3/onramp/crypto", payload);
        return response.data;
    }


    public async createMobileMoneyOfframp(
        data: any

    ) {

        const { phoneNumber, networkProvider, currency, chain, accountName, token, cryptoAmount, referenceId, senderAddress } = data;
        const payload = {
            "mobileMoneyReceiver": {
                "networkProvider": networkProvider,
                "phoneNumber": phoneNumber,
                "accountName": accountName
            },
            "currency": currency,
            "chain": chain,
            "token": token,
            "cryptoAmount": cryptoAmount,
            "referenceId": referenceId,
            "senderAddress": senderAddress
        };

        console.log(`KOTANIinfo`, payload)
        const response = await post("api/v3/offramp", payload);
        console.log(`KOTANIinfoResponse`, response)

        return response.data;
    }

    public async getOnrampCryptoStatus(referenceId: string) {
        const response = await get(`api/v3/onramp/crypto/${referenceId}`);
        return response.data;
    }
    public async getOnrampStatus(referenceId: string) {
        const response = await get(`api/v3/onramp/${referenceId}`);
        return response.data;
    }
    public async getOfframpStatus(referenceId: string) {
        const response = await get(`api/v3/offramp/${referenceId}`);
        return response.data;
    }
    public async MobileMoneyDepositStatus(referenceId: string) {
        const response = await get(`api/v3/deposit/mobile-money/${referenceId}`);
        return response.data;
    }




}

export default new KotaniPay();
