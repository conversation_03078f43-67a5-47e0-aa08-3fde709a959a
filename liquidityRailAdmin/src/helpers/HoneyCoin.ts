import RequestHelper from "./request.helper";
import { RateResponse } from "../interfaces/rate.interfaces";
import axios from "axios";
import dotenv from 'dotenv';
dotenv.config();

class HoneyCoin {
  private mainURL: string;
  private mainCryptoURL: string;
  private mainRequestHeader: Record<string, string>;

  constructor() {
    this.mainURL = process.env.HONEYCOIN_API_URL ?? "";
    this.mainCryptoURL = process.env.HONEYCOIN_CRYPTO_API_URL ?? "";
    this.mainRequestHeader = {
      "Accept": "application/json",
      "Content-Type": "application/json"
    };
  }

  private async request(
    method: "get" | "post" | "put",
    endpoint: string,
    data: any = {},
    crypto: boolean = false
  ) {

    const baseUrl = crypto ? `${this.mainCryptoURL}`:`${this.mainURL}`;
    const mainUrl = `${baseUrl}${endpoint}`.replace(/ /g, "");
    RequestHelper.setEndpoint(mainUrl);
    console.log("base url ---- ", baseUrl, endpoint, method);

    const tokenResponse: any = await this.generateHoneycoinToken();
    const token: any         = tokenResponse?.token;
    if (!token) throw new Error("Failed to generate Honeycoin token");
    this.mainRequestHeader["Authorization"] = `Bearer ${token}`;
    RequestHelper.setHeaders(this.mainRequestHeader);
    if (data) RequestHelper.setData(data);
    await RequestHelper[`${method}Request`]();
    const errors: any   = await RequestHelper.getErrors();
    const respData: any = await RequestHelper.getResults();    
    return errors?.status === "error" ? errors : respData;
  }

  
  public async generateHoneycoinToken() {
    const endpoint: string = "/auth/generate-bearer-token";
    const data: any = {
      publicKey: `${process.env.HONEYCOIN_PUBLIC_KEY}`,
      "api-key": `${process.env.HONEYCOIN_API_KEY}`,
    };
    axios.defaults.headers.common['api-key'] = `${process.env.HONEYCOIN_API_KEY}`;
    return axios.post(`${this.mainURL}${endpoint}`, data).then((res: any) => {
      if(res?.data?.success)  return {
                                        status: "success",
                                        token: res?.data?.token
                                      }
      return {
        status: "error",
        message: res?.data?.message
      }

    }).catch((err: any) => {
      console.log("------------------------- ", err)
      return {
        status: "error",
        message: err?.message ?? "Error generating token"
      }
    })
    // return await this.request("post", endpoint, data);
  }
  
  public async getCountries() {
    const endpoint: string = `/utilities/countries`;
    return await this.request("get", endpoint);
  }

  // get list of all banks per country code ie KE, NG, GH, ZA, etc
  public async getBanks(country: string) {
    const endpoint: string = `/utilities/banks?country=${country}`;
    return await this.request("get", endpoint);
  }

  public async getBankBranch(bankId: string) {
    const endpoint: string = `/utilities/branches?bankId=${bankId}`;
    return await this.request("get", endpoint);
  }

  // mobile money providers
  public async getMobileMoneyProviders(currency: string = "") {
    const endpoint: string = `/utilities/momo/providers?currency=${currency}`;
    return await this.request("get", endpoint);
  }

  public async getUsers() {
    const endpoint: string = "/users";
    return await this.request("get", endpoint);
  }

  public async getTransactions() {
    const endpoint: string = "/transactions";
    return await this.request("get", endpoint);
  }

  public async getATransaction(transactionId: string) {
    const endpoint: string = `/transactions/${transactionId}`;
    return await this.request("get", endpoint);
  }

  //  collection functions
  public async createCollection(
    amount: number,
    phoneNumber: string,
    currency: string,
    externalReference: string,
    momoOperatorId: string = "" ,
    externalAccountId: string = "",
    walletCurrency: string = ""
  ) {

    const endpoint: string = "/fiat/deposit/momo";
    const data = {
      amount: amount,  // int32
      phoneNumber: phoneNumber, // In ISO Format - '+' example: ************
      currency: currency, // string
      externalReference: externalReference, // string
      momoOperatorId: momoOperatorId, // string
      // externalAccountId: externalAccountId, // string
      walletCurrency: walletCurrency
    };
    return await this.request("post", endpoint, data);
  }

  public async verifyCollect(transactionId: any) {
    const endpoint: string = `fiat/deposit/${transactionId}/validate-otp`;
    return await this.request("post", endpoint, {}, false);
  }

  // payout functions
  public async createPayout(
    amount: number,
    currency: string,
    country: string,
    externalReference: string,
    accountName: string,
    accountNumber: string,
    code: string,
    destination: string
  ) {
    const endpoint: string = "/fiat/payout";
    const data = {
      amount: amount,
      currency: currency,
      country: country,  // ISO 3166-1 alpha-2 code 
      externalReference: externalReference,
      payoutMethod: {
        accountName: accountName, // registered account name
        accountNumber: accountNumber, // phone code + phone number (no plus sign)
        code: code // optional. required for some currencies.  
      },
      destination: destination  // "Bank Account" or "MoMo"
    };
    return await this.request("post", endpoint, data);
  }

  // off ramp and exchange rates 
  public async getFXRate(fromCurrency: string, toCurrency: string = "UGX", amount: number ) {
    const endpoint: string = `/utilities/rates?from=${fromCurrency}&to=${toCurrency}&amount=${amount}`;
    return await this.request("get", endpoint);
  }

  public async getOffRampRate(fromCurrency: string, toCurrency: string = "UGX", amount: number) {
    
    const endpoint: any = `/utilities/rates?from=${fromCurrency}&to=${toCurrency}&amount=${amount}`;
    let HoneyCoinResponse: any = await this.request("get", endpoint);
    HoneyCoinResponse = HoneyCoinResponse?.data;
    const status = HoneyCoinResponse?.success;
    if (status) {
        const quoteInfo: RateResponse = {
            provider: "honeycoin",
            providerQuoteId: HoneyCoinResponse?.data?.id || "",
            from: fromCurrency,
            providerId: 2,
            to: toCurrency, 
            fiatAmount:   HoneyCoinResponse?.data?.convertedAmount,
            toAmount:     HoneyCoinResponse?.data?.convertedAmount,
            cryptoAmount: HoneyCoinResponse?.data?.cryptoAmount ?? amount,
            fee:          HoneyCoinResponse?.data?.fee || 0,
            quoteId:      HoneyCoinResponse?.data?.id || "",
            expiresAt:    "",
            quotedPrice:  HoneyCoinResponse?.data?.conversionRate || ""
        };
        return quoteInfo;
    }
}



  public async offRamp(
    senderAmount: number,
    senderCurrency: string,
    receiverCurrency: string = "UGX",
    chain: string,
    email: string,
    country: string,
    destination: string,
    externalReference: string,
    payoutMtdCountry: string,
    payoutMtdProviderId: string,
    payoutMtdAccountNumber: string,
    payoutMtdAccountName: string 
  ) {
    
    const endpoint: string = "/minting/offramp";
    const data = {
      senderAmount: senderAmount,
      senderCurrency: senderCurrency,
      receiverCurrency: receiverCurrency,
      chain: chain,
      email: email,
      country: country,
      destination: destination,
      payoutMethod: {
        country: payoutMtdCountry,
        destination: payoutMtdProviderId,
        accountNumber: payoutMtdAccountNumber,
        accountName: payoutMtdAccountName
      },
      externalReference: externalReference ?? await this.uniqueReference()
    }



    console.log("off ramp data ---- ", data, endpoint);
    return await this.request("post", endpoint, data, true);

  }

  public async paymentWebhook(webhookStatus: string) {
    const endpoint: string = "/utilities/simulate-webhook";
    const data = {
      webhookStatus: webhookStatus
    };
    return await this.request("post", endpoint, data, false);
  }

  public async getWebhooks() {
    const endpoint: string = "/webhooks";
    return await this.request("get", endpoint);
  }

  public async resendWebhook(webhookId: string) {
    const endpoint: string = `/fiat/payoutwebhooks/${webhookId}/resend`;
    return await this.request("post", endpoint, {}, false);
  }

  public async getWebhook(webhookId: string) {
    const endpoint: string = `/webhook/${webhookId}`;
    return await this.request("get", endpoint);
  }

  public async uniqueReference() {
    return `MUDA_PAYMENT${Math.random().toString(36).substring(2, 15)}${Math.random().toString(36).substring(2, 15)}`;
  }
}

export default new HoneyCoin();
