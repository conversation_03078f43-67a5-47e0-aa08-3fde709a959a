import fetch from 'node-fetch';
import axios from "axios";


class RequestHelper {

  private url:            any = "";
  private requestType:    any = "";
  private requestHeaders: any = {}; 
  private requestOptions: any = {}; 
  private requestData:    any = {}; 
  static data:   any          = {};
  static errors: any          = {};


  constructor() {
    this.requestHeaders =   { 'content-type': 'application/json', accept : 'application/json' }
  }

  public async postRequest() {
      
      let content:any =  "";                              
      return new Promise(async (resolve, reject) => {
          try{
              
            let  responseData : any = ""; 
            const data =  this.requestData     //JSON.stringify()
            let config = {
                            method:   'post',
                            url:      this.url,
                            headers:  this.requestHeaders,
                            data :    data
                         };

            responseData = await axios.request(config)
                                      .then((response: any) => {
                                        
                                        const theDate: any = {
                                                                "data": response.data,
                                                                "status": true
                                                            }
                                        RequestHelper.data = theDate;
                                        return theDate;

                                      })
                                      .catch((error: any) => {
                                        
                                        const theDate: any = {
                                                               "data": error?.response?.data?.data,
                                                               "message": error?.response?.data?.message,
                                                               "status": false
                                                             }
                                        RequestHelper.data = theDate;
                                        return theDate;

                                      });
                                      
            return resolve(responseData) 

          } catch (e) {
            
            // console.log("post error", e);
            //return reject({"message":"cannot find content", 'error':'cannot find content', "status": false});
            
             return reject({ "data":   'cannot find content',
                             "message": "cannot find content",
                             "status": false });
          }
      }); 
 
  }




  public async getRequest(){

    let content:any =  "";                              
    return new Promise(async (resolve, reject) => {
        try{
            
          let  responseData : any = ""; 
          const data =  this.requestData; 
          let config = {
                          method:        'GET',
                          url:           this.url,
                          headers:       this.requestHeaders,
                          data :         data
                       };                    
          responseData = await axios.request(config)
                                    .then((response: any) => {
                                        
                                        const theDate: any = {
                                                                "data": response.data,
                                                                "status": true
                                                            }
                                        RequestHelper.data = theDate;
                                        return theDate;

                                      })
                                      .catch((error: any) => {
                                        
                                        const theDate: any = {
                                                               "data": error?.response?.data?.data,
                                                               "message": error?.response?.data?.message,
                                                               "status": false
                                                             }
                                        RequestHelper.data = theDate;
                                        return theDate;


                                      });

          return resolve(responseData) 

        } catch (e) {

             return reject({ "data":   'can not find content',
                             "message": "cannot find content",
                             "status": false });
        }
    }); 

  }






  public async putRequest(){
    
      let content:any =  "";                              
      return new Promise(async (resolve, reject) => {
          try{
              
            let  responseData : any = ""; 
            const data =  this.requestData //JSON.stringify()
            let config = {
                            method:        'put',
                            url:           this.url,
                            headers:       this.requestHeaders,
                            data :         data
                         };
            responseData = await axios.request(config)
                                      .then((response: any) => {
                                        
                                        const theDate: any = {
                                                                "data": response.data,
                                                                "status": true
                                                            }
                                        RequestHelper.data = theDate;
                                        return theDate;

                                      })
                                      .catch((error: any) => {

                                        const theDate: any = {
                                                               "data": error?.response?.data?.data,
                                                               "message": error?.response?.data?.message,
                                                               "status": false
                                                             }
                                        RequestHelper.data = theDate;
                                        return theDate;


                                      });
            return resolve(responseData) 

          } catch (e) {
             return reject({ "data":   'cannot find content',
                             "message": "cannot find content",
                             "status": false });
          }
      }); 

  }



  public async patchRequest(){
    
      let content:any =  "";                              
      return new Promise(async (resolve, reject) => {
          try{
              
            let  responseData : any = ""; 
            const data =  this.requestData
            let config = {
                            method:        'PATCH',
                            url:           this.url,
                            headers:       this.requestHeaders,
                            data :         data
                         };
            responseData = await axios.request(config)
                                      .then((response: any) => {
                                        
                                        const theDate: any = {
                                                                "data": response.data,
                                                                "status": true
                                                            }
                                        RequestHelper.data = theDate;
                                        return theDate;

                                      })
                                      .catch((error: any) => {

                                        const theDate: any = {
                                                               "data": error?.response?.data?.data,
                                                               "message": error?.response?.data?.message,
                                                               "status": false
                                                             }
                                        RequestHelper.data = theDate;
                                        return theDate;


                                      });
            return resolve(responseData) 

          } catch (e) {
             return reject({ "data":   'cannot find content',
                             "message": "cannot find content",
                             "status": false });
          }
      }); 

  }





  public async deleteRequest(){
    
      let content:any =  "";                              
      return new Promise(async (resolve, reject) => {
          try{
              
            let  responseData : any = ""; 
            const data =  this.requestData; //JSON.stringify()
            
            let config = {
                            method:        'delete',
                            url:           this.url,
                            headers:       this.requestHeaders,
                            data :         data
                         };
            responseData = await axios.request(config)
                                     .then((response: any) => {
                                        
                                        const theDate: any = {
                                                                "data": response.data,
                                                                "status": true
                                                            }
                                        RequestHelper.data = theDate;
                                        return theDate;

                                      })
                                      .catch((error: any) => {

                                        const theDate: any = {
                                                               "data": error?.response?.data?.data,
                                                               "message": error?.response?.data?.message,
                                                               "status": false
                                                             }
                                        RequestHelper.data = theDate;
                                        return theDate;


                                      });
            return resolve(responseData) 

          } catch (e) {

             return reject({ "data":   'cannot find content',
                             "message": "cannot find content",
                             "status": false });
          }
      }); 

  }


     
  setEndpoint(url: any){
     this.url = url;
  }


  setData(data: any){
     this.requestData = data;
  }

  setHeaders(headers: any){
     this.requestHeaders = headers;
  }


  getResults(){
     return RequestHelper.data;
  } 

  getErrors(){
     return RequestHelper.errors;
  }

}

export default new RequestHelper();