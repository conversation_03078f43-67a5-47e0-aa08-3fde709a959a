import BaseModel from "./base.model";
import { get, post } from "./httpRequest";
import { v4 as uuidv4 } from 'uuid';
import EmailSender from './email';
import TwoFactorAuthHelper from "./2fa.helper";
import CryptoJS from "crypto-js";
import StellarSdk from "stellar-sdk";
export type WebhookData = {
    type: string;
    statusCode: number;
    message: string;
    client_id: string;
    trans_type: string;
    timestamp: string;
    reference_id: string;
    status: string;
    amount: string;
    fee: string;
    currency: string;
    sender_account: string;
    receiver_account: string;
    transaction_id: string;
    meta: string;
    chainInfo?: ChainInfo;
}
type ChainInfo = {
    from_address: string;
    to_address: string;
    amount: string;
    asset_code: string;
    contract_address?: string;
    hash?: string;
    state?: string;
    direction?: string;
}

import jwt from "jsonwebtoken";

const SECRET_KEY   = process.env.SECRET_KEY || "DQSJTOOZWCZY2F32762NZRSOD64Y6Q7W"
const COMMON_ROLE_CLIENT_ADMIN  = {"id": "0000-0000-0000-0001", "name": "Admin (DF)", "details": "Default Admin", "status": "active"}
const COMMON_ROLE_CLIENT        = {"id": "0000-0000-0000-0002", "name": "User (DF)", "details": "Default User", "status": "active"}
const COMMON_ROLE_ADMIN        = {"id": "0000-0000-0000-0003", "name": "Admin (DF)", "details": "Default Admin", "status": "active"}



import { sendNotification } from "./FCM";

const mailer = new EmailSender();
export default class Model extends BaseModel {
    async GetIssuerAccount(asset_code: string, arg1: string) {
        return process.env.STELLAR_PAYOUT_ISSUER_SECRET
    }

    // main admin with all client access
    async defaultRole() {
        return COMMON_ROLE_CLIENT_ADMIN;
    }

    // main client with view client access
    async defaultClientRole() {
        return COMMON_ROLE_CLIENT;
    }

    // main admin  account role
    async defaultAdminRole() {
        return COMMON_ROLE_ADMIN;
    }

    async randomPassString(lenth: any) {
        let result = '';
        for (let i = 0; i < lenth; i++) {
            result += Math.floor(Math.random() * 10);
        }
        return result;
    }

    encryptPassword(password: string) {
        const secretKey = process.env.SECRET_KEY || "your-secret-key";
        const encrypteNewdPassword = CryptoJS.AES.encrypt(password, secretKey).toString();
        return encrypteNewdPassword;
    }
    async userFaAuthAccountStatus(data: any) {
        try {

            const userID = (data.user_type === 'admin' || data.user_type === "") ? data.clientId : data.userId;
            const client: any = await this.callQuery(`
                        SELECT * FROM user_2fa
                        WHERE user_id = '${userID}' AND user_type = '${data.user_type}' AND deleted_at IS NULL
                        ORDER BY created_at DESC
                        LIMIT 1
                    `);
            return client;

        } catch (error: any) {
            return;
        }
    }

    async createWallet(clientId: string) {
        try {
            const keys: any = await this.callQuery(`client_wallets`, `client_wallets='${clientId}'`)
            if (keys.length > 0) {
                return keys[0]
            }
            const keypair = StellarSdk.Keypair.random();
            const publicKey = keypair.publicKey();
            const secretKey = keypair.secret();
            const encryptedSecretKey = CryptoJS.AES.encrypt(secretKey, SECRET_KEY).toString();
            const decryptedSecretKey = CryptoJS.AES.decrypt(encryptedSecretKey, SECRET_KEY).toString(CryptoJS.enc.Utf8);
            console.log(`decSSecretKey`, decryptedSecretKey)
            const apiKey = uuidv4();
            const apiKeyData = {
                client_id: clientId,
                public_key: publicKey,
                secret_key: encryptedSecretKey,
            };

            await this.insertData("client_wallets", apiKeyData);
            return this.makeResponse(201, "API keys generated successfully", apiKeyData);
        } catch (error: any) {
            console.error("Error generating API keys:", error);
            return this.makeResponse(500, "Server error", error.message);
        }
    }

    makeResponse(status: number, message: string, data: any = null) {
        const response: any = { status, message };
        if (data !== null) {
            response.data = data;
        }
        return response;
    }

    getRandomString() {
        return uuidv4().replace(/-/g, '');
    }

    getTransId() {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let result = '';
        for (let i = 0; i < 15; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }

    async randomPassword(lenth: any) {
        let result = '';
        for (let i = 0; i < lenth; i++) {
            result += Math.floor(Math.random() * 10);
        }
        return result;
    }


    getMySQLDateTime_() {
        return new Date().toISOString().slice(0, 19).replace('T', ' ');
    }

    async updateRoleAccessRights(roleId: string, accessRights: string[]) {

        const EXISTING_RIGHTS: any = await this.callQuery(`SELECT id, access_right_id FROM role_access_rights  WHERE role_id = '${roleId}' AND deleted_at IS NULL`);
        console.log("EXISTING_RIGHTS", EXISTING_RIGHTS);
        const EXISTING_IDS = EXISTING_RIGHTS.map((right: any) => right.access_right_id);
        console.log("accessRights_________  ", accessRights);
        const NEW_IDS = accessRights;

        if (EXISTING_IDS.length > 0) {
            const IDS_TO_DELETE = EXISTING_IDS.filter((id: string) => !NEW_IDS.includes(id));
            if (IDS_TO_DELETE.length > 0) {
                await this.updateData(
                    "role_access_rights",
                    `role_id = '${roleId}' AND access_right_id IN ('${IDS_TO_DELETE.join("','")}')`,
                    { deleted_at: this.getMySQLDateTime_() }
                );
            }
            console.log("EXISTING_RIGHTS TO DELETE", IDS_TO_DELETE);
        }

        let ids_to_add: any = NEW_IDS;
        if (EXISTING_IDS.length > 0) {
            ids_to_add = NEW_IDS.filter((id: string) => !EXISTING_IDS.includes(id));
        }
        console.log("EXISTING_RIGHTS TO ADD", ids_to_add);
        // finally add the rights
        for (const ACCESS_RIGHT_ID of ids_to_add) {
            const ROLE_ACCESS_RIGHT_DATA = {
                id: uuidv4(),
                role_id: roleId,
                access_right_id: ACCESS_RIGHT_ID,
                created_at: this.getMySQLDateTime_()
            };
            await this.insertData("role_access_rights", ROLE_ACCESS_RIGHT_DATA);
        }

    }


    async sendAppNotification(userId: string, operation: string, name = '', otp = '') {
        console.log(`SEND_1`, { userId, operation });



        console.log(`SEND_2`, { userId, operation });

        const messageBody = await this.selectDataQuery("notification_templates", `operation = '${operation}' AND channel != 'EMAIL'`);
        if (messageBody.length === 0) {
            console.log(`SEND_3`, messageBody);
            return this.makeResponse(404, "Operation not found");
        }

        const token = ""
        const message = messageBody[0]['body'];
        const subject = messageBody[0]['title'];

        const newMessage = this.constructSmsMessage(message, name, otp, "", "");
        const data = { title: subject, body: newMessage };

        console.log(`SEND_4`, data);

        const response = await sendNotification(token, data);
        console.log(`SEND_5`, response);

        return false;
    }

    async sendEmail(operation: string, email: string, name = "", otp = "", tableData: any = [], code: string = '') {
        try {
            const messageBody = await this.selectDataQuery("notification_templates", `operation = '${operation}'`);
            if (messageBody.length === 0) {
                return this.makeResponse(404, "Operation not found");
            }

            let listHtml = "<ul>";
            tableData.forEach((item: any) => {
                listHtml += `<li>${item}</li>`;
            });
            listHtml += "</ul>";

            const message = messageBody[0]['body'];
            const subject = messageBody[0]['title'];

            const newMessage = this.constructSmsMessage(message, name, otp, listHtml, code);
            mailer.sendMail(email, subject, subject, newMessage);

            return true;
        } catch (error) {

            console.log('error  sending email   ----- ', error)
            return this.makeResponse(203, "Error fetching company");
        }
    }

    constructSmsMessage(template: string, name: string, otp: string, listHtml: any, code: string): string {
        const data: any = { name, otp, code, listHtml };
        for (const key in data) {
            if (Object.prototype.hasOwnProperty.call(data, key)) {
                template = template.replace(new RegExp(`{${key}}`, 'g'), data[key]);
            }
        }
        return template;
    }

    generateRandom4DigitNumber() {
        return "10" + Math.floor(100000 + Math.random() * 900000);
    }

    generateRandomDigitNumber(length: number) {
        return Math.floor(10066000 + Math.random() * 90066000).toString().slice(0, length);
    }
    async getapikeys(clientId: string) {
        return await this.selectDataQuery("api_keys", `client_id = ${clientId}`);
    }
    async getClientWallet(clientId: string) {
        const wallet = await this.selectDataQuery("client_wallets", `client_id = '${clientId}'`);
        return wallet

    }

    async saveWebhookLog(cl_id: any, clientId: any, transId: any, callbackUrl: any, event: any, webhookData: any, status: any, direction: any = "OUTGOING", provider = "MUDA") {
        try {
            await this.insertData("webhook_logs", {
                cl_id: cl_id,
                client_id: clientId,
                trans_id: transId,
                webhook_url: callbackUrl,
                event: event,
                webhook_data: JSON.stringify(webhookData),
                status_code: status,
                timestamp: this.formatedDate(new Date()),
                direction: direction,
                provider: provider
            });
        } catch (error: any) {
            console.error(`❌ Error in saveWebhookLog:`, error);
            return false;
        }
    }

    formatedDate(updated_at: any) {
        return new Date(updated_at).toISOString().slice(0, 19).replace('T', ' ')
    }

    async updateWebhookLog(cl_id: any, response: any, response_code: any, updated_at: any) {
        try {
            const data = {
                response: JSON.stringify(response),
                response_code: response_code,
                updated_at: this.formatedDate(new Date())
            }
            await this.updateData("webhook_logs", `cl_id = '${cl_id}'`, data)
        } catch (error: any) {
            console.error(`❌ Error in updateWebhookLog:`, error);
            return false;
        }
    }

    async sendWebhook(clientId: any, callbackUrl: any, transId: any, event: any, webhookData: WebhookData) {

        const cl_id = this.getTransId()

        try {
            console.log(`sendWebhook3`, { clientId, callbackUrl, transId, event, webhookData })
            await this.saveWebhookLog(cl_id, clientId, transId, callbackUrl, event, webhookData, 202, "RAIL")

            const response = await post(callbackUrl, webhookData);
            console.log(`sendWebhook5`, { response })
            await this.updateWebhookLog(cl_id, response, response.status, this.formatedDate(new Date()))
            return response;
        } catch (error: any) {
            console.log(`sendWebhook4`, { error })
            await this.updateWebhookLog(cl_id, error, error.status || 500, this.formatedDate(new Date()))
            return false;
        }
    }

    async saveWebhook(
        clientId: string,
        transId: any,
        event: any,
        webhookData: any
    ) {
        try {
            const cl_id = this.getTransId()
            console.log(`saveWebhook1`, { cl_id, clientId, transId, event, webhookData })
            await this.saveWebhookLog(cl_id, clientId, transId, "", "INITIAL", webhookData, 202, "RAIL")
            const clientInfo: any = await this.selectDataQuery(`webhooks`, `client_id='${clientId}'`);
            if (clientInfo.length === 0) {
                console.error(`No webhook URL found for client ID: ${clientId}`);
                return false;
            }
            console.log(`saveWebhook2`, { clientInfo })


            for (let i = 0; i < clientInfo.length; i++) {
                const callbackUrl = clientInfo[i]['callback_url'];
                console.log(`saveWebhook3`, { callbackUrl })
                await this.sendWebhook(clientId, callbackUrl, transId, event, webhookData);
            }
            return true

        } catch (error: any) {
            console.log(`saveWebhook4`, { error })
            return false;
        }
    }

    async getDecryptedApiKey(clientId: string) {
        try {
            const apiKeyRecord = await this.selectDataQuery("client_wallets", `client_id = ${clientId}`);
            if (apiKeyRecord.length === 0) {
                return this.createWallet(clientId);
            }

            return {
                client_id: clientId,
                public_key: apiKeyRecord[0].public_key,
                secret_key: "" // This is now the hashed value
            }

        } catch (error: any) {
            console.error("Error retrieving API keys:", error);
            return null
        }
    }

    async getBusinessByEmail(email: string) {
        return await this.selectDataQuery("clients", `contact_email = '${email}'`);
    }

    validateDomain(domain: string) {
        const domainRegex = /^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
        const cleanDomain = domain.replace(/^(?:https?:\/\/)?(?:www\.)?/, '').split('/')[0];
        return domainRegex.test(cleanDomain);
    }

    validateAndCleanDomain(domain: string) {
        return domain.replace(/^(?:https?:\/\/)?(?:www\.)?/, '').split('/')[0];
    }

    doesEmailDomainMatch(email: string, domain: string) {
        return email.split('@')[1] === domain;
    }

    async getClientInfo(clientId: string) {
        return await this.selectDataQuery("clients", `client_id = ${clientId}`);
    }

    async getUserClientByUserId(userId: any) {
        return await this.selectDataQuery(`client_logins`, `id='${userId}'`)
    }

    async getUserClientLogin(email: any) {
        return await this.selectDataQuery(`client_logins`, `email='${email}'`)
    }

    async getCampanyOwner(clientId: any, userId: any) {
        return await this.selectDataQuery(`client_logins`, `client_id='${clientId}' AND id='${userId}' and role='owner'`)
    }

    async sendUserAuthToken(userId: string) {
        const user = await this.getUserClientByUserId(userId)
        console.log(`client3`, user)

        if (user.length === 0) {
            return false
        }
        const token = await this.getOtp(user[0].email, userId, 'code')
        this.sendEmail("AUTH_TOKEN", user[0].email, token, token);
        return this.makeResponse(200, `Token sent successfully to ${user[0].email}`);

    }

    async sendClientAuthToken(clientId: string) {
        const client = await this.getClientInfo(clientId)
        if (client.length === 0) {
            return false
        }
        const token = await this.getOtp(client[0].email, clientId, 'code')
        this.sendEmail("AUTH_TOKEN", client[0].email, token, token);
        return this.makeResponse(200, `Token sent successfully to ${client[0].email}`);

    }

    async getOtp(email: string, user_id: string, otpType: string = 'otp') {
        console.log(`client4`, email, user_id, otpType)

        const user: any = await this.selectDataQuery("user_otp", `email = '${email}' and user_id = '${user_id}'`);
        let otp = this.generateRandom4DigitNumber().toString();

        if (otpType === 'code') {
            otp = this.generateRandomDigitNumber(8);
        }

        console.log(`client5`, { email, otp });

        if (user.length === 0) {
            await this.insertData('user_otp', { user_id, email, otp });
        } else {
            await this.updateData('user_otp', `email = '${email}' and user_id = '${user_id}'`, { otp });
        }
        
        return otp;
    }

    async saveNotification(title: string, companyId: string, message: any) {
        const newNotification = { title, companyId, message };
        return await this.insertData('notifications', newNotification);
    }

    async getDocVerifiers(docId: string) {
        return await this.callQuery(`SELECT * FROM verifiers WHERE doc_id='${docId}'`);
    }

    async confirmUser2Fa(data: any) {
        try {


            const user_type: any = data?.user_type || 'admin';
            const client: any = await this.userFaAuthAccountStatus({ ...data, user_type: user_type })
            const responseData: any = await TwoFactorAuthHelper.verifySecret(client[0]?.secret, data.token)
            if (!responseData?.status) {
                return { status: false, message: 'Invalid 2fa token' }
            }
            return { status: true, message: '' };
        } catch (error: any) {
            return { status: true, message: 'Invalid 2fa token' };
        }
    }
}
