import Model from "../helpers/model";
import TwoF<PERSON><PERSON><PERSON><PERSON>elper from "../helpers/2fa.helper";
import EmailSender from "../helpers/email.helper";
import { v4 as uuidv4 } from "uuid";
import jwt from "jsonwebtoken";
import CryptoJS, { algo } from "crypto-js";
import { z } from "zod";
import StellarService from "../helpers/StellarService";  // Moved to wallet service
import { LiquidityRailService } from "../services/liquidityrail.service";
import WalletService from "../services/wallet.service";
import RatesService from "../helpers/exchangerates.helper";
import { formatDateForMySQL, getCurrentDateMySQL, getFirstDayOfCurrentMonth, isToday, getFirstDayOfNextMonth } from '../helpers/dateHelper';
// import { createAddress, createWallet, listWalletsForVault, createWalletAddress } from "../intergrations/Utilia";  // Moved to wallet service
// Define CURRENT_DATE constant for consistent date handling
const CURRENT_DATE = new Date().toISOString().split('T')[0]; // Returns YYYY-MM-DD format

const SECRET_KEY = ""; 
const SECRET_KEY_CLIENT = process.env.SECRET_KEY || "";
const sendmail = new EmailSender()
const ratesService = new RatesService();
const liquidityRailService = new LiquidityRailService();

type User_ = {
  email: string;
  password: string;
  role: string;
  first_name: string;
  last_name: string;
};

type RoleUpdateData = {
  name: string;
  details?: string;
  status: 'active' | 'inactive';
  updated_at: string;
};

type UserData = {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  role: string;
  role_details: {
    id: string;
    name: string;
    details: string;
    status: string;
    access_rights: any[];
  };
  status: string;
  updated_at: string;
  created_at: string;
};
interface balanceInterface {
  balance: string;
  asset: string;
}

export default class Admin extends Model {

  async thirdpartyBalances() {
    const dummyBalances: balanceInterface[] = [
      {
        balance: "100",
        asset: "USD"
      },
      {
        balance: "100",
        asset: "UGX"
      },

    ]

    const accounts = ["quidax", "utilia", "muda", "pegasus_out", "pegasus_in"]
    const getQuidaxBalances = dummyBalances
    const getUtiliaBalances = dummyBalances
    const getMudaBalances = dummyBalances
    const getPegasusOutBalances = dummyBalances
    const getPegasusInBalances = dummyBalances
    return this.makeResponse(200, "Balances fetched successfully", {
      quidax: getQuidaxBalances,
      utilia: getUtiliaBalances,
      muda: getMudaBalances,
      pegasus_out: getPegasusOutBalances,
      pegasus_in: getPegasusInBalances
    });
  }

  async saveNetworks(chain: { name: any; displayName: any; caipDetails: any; testnet: any; nativeAsset: any; custom: any; }) {


    const network: any = await this.callQuery(`select * from utilia_networks where name = '${chain.name}'`);
    if (network.length > 0) {
      return this.makeResponse(200, "Network already exists", network);
    }
    const response = await this.insertData("utilia_networks", chain);
    return this.makeResponse(200, "Network saved successfully", response);
  }
  private readonly TRANSACTION_FEES_TABLE = 'custome_fees';

  constructor() {
    super();
  }

  async issueTokens(data: any) {
    return this.makeResponse(401, "SUCCESS", data);
  }

  

  private getMySQLDateTime() {
    return new Date().toISOString().slice(0, 19).replace('T', ' ');
  }

  async getAssetHolders(currency: string) {
    // Get all active holders from Stellar
    const hodlers = await new StellarService().getAssetHolders(currency.toUpperCase());
    const users: any = [];

    // Get all client wallets from database
    const allClientWallets: any = await this.callQuery(`
      SELECT 
        w.public_key, 
        c.business_name, 
        c.client_id 
      FROM client_wallets w 
      INNER JOIN clients c ON w.client_id = c.client_id
    `);

    // Create map of existing funded holders
    const existingHolders = new Set(hodlers.map(h => h.accountId));

    // Process active holders from Stellar
    for (const holder of hodlers) {
      const publicKey = holder.accountId;
      const clientInfo = allClientWallets.find((w: any) => w.public_key === publicKey);

      users.push({
        wallet: holder,
        clientId: clientInfo?.client_id || null,
        businessName: clientInfo?.business_name || null
      });
    }

    // Add unfunded wallets that aren't in Stellar response
    for (const wallet of allClientWallets) {
      if (!existingHolders.has(wallet.public_key)) {
        users.push({
          wallet: {
            accountId: wallet.public_key,
            balance: "0.0000000", // Unfunded wallet
            cBalance: "0"
          },
          clientId: wallet.client_id,
          businessName: wallet.business_name
        });
      }
    }

    return this.makeResponse(200, "Holders fetched successfully", users);
  }


  async getAccessClients(id: any, data?: any) {
    try {
      const { page = 1, limit = 10, search = '' } = data || {};
      
      // Calculate offset for pagination
      const offset = (page - 1) * limit;
      
      // Build search condition
      let searchCondition = '';
      if (search && search.trim() !== '') {
        searchCondition = `AND (
          cl.email LIKE '%${search}%' OR 
          cl.first_name LIKE '%${search}%' OR 
          cl.last_name LIKE '%${search}%' OR
          cl.role LIKE '%${search}%'  OR
          rl.name LIKE '%${search}%'
        )`;
      }
      
      // Get total count for pagination
      const countQuery = `SELECT COUNT(*) as total FROM client_logins WHERE client_id = '${id}' ${searchCondition}`;
      const totalCount: any = await this.callQuery(countQuery);
      const total = totalCount[0]?.total || 0;
      
      // Get paginated results
      const query = `
        SELECT cl.*, rl.name as role_name, rl.details as role_details FROM client_logins cl
        LEFT JOIN roles rl ON cl.role = rl.id
        WHERE cl.client_id = '${id}' 
        ${searchCondition}
        ORDER BY cl.created_at DESC 
        LIMIT ${limit} OFFSET ${offset}
      `;
      
      const clientLogins: any = await this.callQuery(query);
      
      // Calculate pagination metadata
      const totalPages = Math.ceil(total / limit);
      const hasNextPage = page < totalPages;
      const hasPrevPage = page > 1;
      
      const paginationInfo = {
        currentPage: page,
        totalPages,
        totalItems: total,
        itemsPerPage: limit,
        hasNextPage,
        hasPrevPage,
        nextPage: hasNextPage ? page + 1 : null,
        prevPage: hasPrevPage ? page - 1 : null
      };

      const defaultRole: any = await this.defaultRole();
      clientLogins.forEach((client: any) => {
          if(client.role === defaultRole?.id) {
            client.role_name    = defaultRole.name;
            client.role_details = defaultRole.details;
          }
      });

      return this.makeResponse(200, "Access clients fetched successfully", {
        data: clientLogins,
        pagination: paginationInfo
      });
    } catch (error: any) {
      console.error("Error fetching access clients:", error);
      return this.makeResponse(500, "Error fetching access clients");
    }
  }

  async getPendingDeposits() {
    try {
      const pendingDeposits1 = await this.callQuery(`select transactions.*, clients.business_name
                                                     FROM transactions 
                                                     LEFT JOIN clients ON transactions.client_id = clients.client_id
                                                     where transactions.status = 'pending'
                                                     AND (transactions.service_name = 'BANK_DEPOSIT' OR transactions.service_name = 'SWAP')
                                                     ORDER BY transactions.created_at DESC `);
      // const pendingDeposits = await this.callQuery(`select * from transactions where  status = 'pending' AND (trans_type = 'BANK_DEPOSIT' OR trans_type='SWAP') order by id desc `);
      return this.makeResponse(200, "Pending deposits fetched successfully", pendingDeposits1);
    } catch (error: any) {
      console.error("Error fetching pending deposits:", error);
      return this.makeResponse(500, "Error fetching pending deposits");
    }
  }

  async addApprovalDeposits(id: string, data: any) {
    try {

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(400, responseData?.message);
      }

      const pendingDeposits: any = await this.callQuery(`select * from transactions where trans_id = '${id}' AND status = 'PENDING' and service_name = 'BANK_DEPOSIT'`);
      if (pendingDeposits.length === 0) {
        return this.makeResponse(400, "Pending deposit not found");
      }
      const { status } = data;
      let updateData = {};
      if (status === "approved") {
        updateData = {
          status: "SUCCESS"
        }
        const walletService = new WalletService();
        const response = await walletService.issueTokens(id);
        console.log('response', response)
        return this.makeResponse(200, "Pending deposit was approved successfully", response);
      } else if (status === "rejected") {
        updateData = {
          status: "failed",
          memo: 'Admin Rejected'
        }
        await this.updateData("transactions", `trans_id = '${id}'`, updateData);
      }
      return this.makeResponse(200, `Pending deposit was ${status} successfully`, pendingDeposits);

    } catch (error: any) {
      console.error("Error fetching pending deposits:", error);
      return this.makeResponse(500, "Error fetching pending deposits");
    }
  }


  async transactionReport(data: any) {
    try {
      const { start_date, end_date, currency } = data;
      const transactions = await this.callQuery(`select * FROM transactions  ORDER BY created_at DESC LIMIT 200`);
      return this.makeResponse(200, "Transactions fetched successfully", transactions);
    } catch (error: any) {
      console.error("Error fetching transactions:", error);
      return this.makeResponse(500, "Error fetching transactions");
    }
  }

  async getTransactions() {
    try {

      const transactions = await this.callQuery(`select * FROM transactions WHERE NOT (trans_type = 'BANK_DEPOSIT' AND status = 'pending') ORDER BY created_at DESC LIMIT 200`);
      return this.makeResponse(200, "Transactions fetched successfully", transactions);
    } catch (error: any) {
      console.error("Error fetching transactions:", error);
      return this.makeResponse(500, "Error fetching transactions");
    }
  }


  async getTransactionDetails(id: string) {
    try {
      
      const transactions: any = await this.callQuery(`select * FROM transactions WHERE trans_id = '${id}' LIMIT 1`);
      if(transactions.length === 0) {
        return this.makeResponse(400, "Transaction not found");
      }
      const client: any = await this.callQuery(`select * FROM clients WHERE client_id = '${transactions[0].client_id}'`);
      transactions[0].client = client[0];
      const transactionDetails = {
        transaction: transactions[0]
      }
      return this.makeResponse(200, "Transactions fetched successfully", transactions[0]);
    
    } catch (error: any) {
      console.error("Error fetching transactions:", error);
      return this.makeResponse(500, "Error fetching transactions");
    }
  }



  async getStats(data: any) {
    try {

      const { start_date, end_date, currency } = data;
      // const startDate = start_date ? new Date(start_date) : new Date();
      // const endDate = end_date ? new Date(end_date) : new Date();
      const statsCurrency: any = currency ? currency : 'UGX';
      // const startDateString = startDate.toISOString().split('T')[0];
      // const endDateString = endDate.toISOString().split('T')[0];
      const businessCount: any[] = await this.selectDataQuery("clients", `status = 'active'`);
      const businessInactiveCount: any[] = await this.selectDataQuery("clients", `status = 'inactive'`);
      const businessApprovedCount: any[] = await await this.selectDataQuery("maker_checker", `( entry_type = 'add_business' OR entry_type = 'edit_business' ) AND status = 'approved'`);
      const businessRejectedCount: any[] = await this.selectDataQuery("maker_checker", `( entry_type = 'add_business' OR entry_type = 'edit_business' ) AND status = 'rejected'`);
      const businessPendingCount: any[] = await this.selectDataQuery("maker_checker", `( entry_type = 'add_business' OR entry_type = 'edit_business' ) AND status = 'pending'`);
      

      const transactions_this_week: any = await this.callQuery(`select count(*) as total FROM transactions WHERE status = 'SUCCESS' AND YEARWEEK(created_at, 1) = YEARWEEK(CURDATE(), 1)`);
      const volumes_this_week: any = await this.callQuery(`select SUM(amount) as total FROM transactions WHERE currency = '${statsCurrency}' AND YEARWEEK(created_at, 1) = YEARWEEK(CURDATE(), 1)`);
      const registrations_this_week: any = await this.callQuery(`select count(*) as total FROM clients WHERE status = 'active' AND YEARWEEK(created_at, 1) = YEARWEEK(CURDATE(), 1)`);
      const collections_this_week: any = await this.callQuery(`select count(*) as total FROM transactions WHERE status = 'SUCCESS' AND trans_type='PULL' AND YEARWEEK(created_at, 1) = YEARWEEK(CURDATE(), 1)`);
      const payouts_this_week: any = await this.callQuery(`select SUM(amount) as total FROM transactions WHERE currency = '${statsCurrency}' AND status = 'SUCCESS' AND trans_type='PUSH' AND YEARWEEK(created_at, 1) = YEARWEEK(CURDATE(), 1)`);
      const revenue_this_week: any = await this.callQuery(`select 
                                                              SUM(fee - provider_fees) AS profit FROM transactions WHERE currency = '${statsCurrency}' AND status = 'SUCCESS' AND trans_type='PUSH' AND YEARWEEK(created_at, 1) = YEARWEEK(CURDATE(), 1)`);
      const revenue_earned: any = await this.callQuery(`SELECT 
                                                              DATE_FORMAT(created_at, '%b') AS month,
                                                              SUM(amount) AS total_revenue,
                                                              SUM(fee) AS muda_fees,
                                                              SUM(provider_fees) AS provider_fees,
                                                              SUM(fee - provider_fees) AS profit
                                                            FROM transactions
                                                            WHERE status = 'SUCCESS'
                                                              AND created_at >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
                                                            GROUP BY YEAR(created_at), MONTH(created_at)
                                                            ORDER BY YEAR(created_at), MONTH(created_at)`);
                                
    const user_growth: any = await this.callQuery(`SELECT 
                                                            DATE_FORMAT(created_at, '%b') AS month,
                                                            COUNT(*) AS total_clients
                                                          FROM clients
                                                          WHERE status = 'active'
                                                            AND created_at >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
                                                          GROUP BY DATE_FORMAT(created_at, '%b'), YEAR(created_at), MONTH(created_at)
                                                          ORDER BY YEAR(created_at), MONTH(created_at)`);

      const hardcodedStats = {
        collections:  collections_this_week[0]?.total ?? 0,
        payouts:  payouts_this_week[0]?.total ?? 0,
        revenue: revenue_this_week[0]?.profit ?? 0,
        transactions: transactions_this_week[0]?.total ?? 0,
        transactions_this_week: transactions_this_week[0]?.total ?? 0,
        volumes_this_week: (volumes_this_week[0]?.total !== null) ? `${volumes_this_week[0]?.total} ${statsCurrency}` : `0 ${statsCurrency}`,
        registrations_this_week: registrations_this_week[0]?.total ?? 0,

        businesses: businessCount.length ?? 0,
        businessInactiveCount: businessInactiveCount.length ?? 0,
        businessApprovedCount: businessApprovedCount.length ?? 0,
        businessRejectedCount: businessRejectedCount.length ?? 0,
        businessPendingCount: businessPendingCount.length ?? 0,
        revenue_earned: revenue_earned,
        user_growth: user_growth

      };

      return this.makeResponse(200, "Stats fetched successfully", hardcodedStats);
    } catch (error: any) {
      console.error("Error fetching stats:", error);
      return this.makeResponse(500, "Error fetching stats");
    }
  }

  async getStatsVolumesGraphs(data: any) {
    try {
      const { start_date, end_date, currency } = data;
      const statsCurrency: any = currency ? currency : 'UGX';

      const transactions_this_week: any = await this.callQuery(`select count(*) as total FROM transactions WHERE status = 'SUCCESS' AND YEARWEEK(created_at, 1) = YEARWEEK(CURDATE(), 1)`);
      const volumes_this_week: any = await this.callQuery(`select SUM(amount) as total FROM transactions WHERE currency = '${statsCurrency}' AND YEARWEEK(created_at, 1) = YEARWEEK(CURDATE(), 1)`);
    } catch (error: any) {  
      console.error("Error fetching stats graphs:", error);
      return this.makeResponse(500, "Error fetching stats graphs");
    }
  }

  async getStatsRevenueGraphs(data: any) {
    try {
      const { start_date, end_date, currency } = data;
      const statsCurrency: any = currency ? currency : 'UGX';

      const transactions_this_week: any = await this.callQuery(`select count(*) as total FROM transactions WHERE status = 'SUCCESS' AND YEARWEEK(created_at, 1) = YEARWEEK(CURDATE(), 1)`);
      const volumes_this_week: any = await this.callQuery(`select SUM(amount) as total FROM transactions WHERE currency = '${statsCurrency}' AND YEARWEEK(created_at, 1) = YEARWEEK(CURDATE(), 1)`);
    } catch (error: any) {  
      console.error("Error fetching stats graphs:", error);
      return this.makeResponse(500, "Error fetching stats graphs");
    }
  }

  
  async getStatsUsersGraphs(data: any) {
    try {
      const { start_date, end_date, currency } = data;
      const statsCurrency: any = currency ? currency : 'UGX';

      const transactions_this_week: any = await this.callQuery(`select count(*) as total FROM transactions WHERE status = 'SUCCESS' AND YEARWEEK(created_at, 1) = YEARWEEK(CURDATE(), 1)`);
      const volumes_this_week: any = await this.callQuery(`select SUM(amount) as total FROM transactions WHERE currency = '${statsCurrency}' AND YEARWEEK(created_at, 1) = YEARWEEK(CURDATE(), 1)`);
    } catch (error: any) {  
      console.error("Error fetching stats graphs:", error);
      return this.makeResponse(500, "Error fetching stats graphs");
    }
  }

  async getBusinessWallet(client_id: string, data: any) {
    try {
      const { start_date, end_date, currency } = data;
      const wallets: any = await this.callQuery(`select public_key, created_on FROM client_wallets WHERE client_id='${client_id}'`)
      return this.makeResponse(200, "Client wallets fetched successfully", wallets);
    } catch (error: any) {
      console.error("Error fetching business wallet:", error);
    }
  }

  private validateBusinessData(data: any) {
    const businessSchema = z.object({
      business_name: z.string().min(3),
      phone_number: z.string().min(10).max(15),
      address: z.string().optional(),
      contact_person_name: z.string().min(3),
      contact_email: z.string().email(),
      contact_phone: z.string().min(10).max(15),
    });
    return businessSchema.safeParse(data);
  }

  private validateBankData(data: any) {
    const bankSchema = z.object({
      bank_name: z.string().min(1, "Bank name is required"),
      account_name: z.string().min(1, "Account name is required"),
      account_number: z.string().min(1, "Account number is required"),
      swift_code: z.string().min(1, "SWIFT code is required"),
      country: z.string().min(1, "Country is required"),
      currency: z.string().min(1, "Currency is required"),
      branch_name: z.string().optional(),
      reference_code: z.string().optional(),
    });
    return bankSchema.safeParse(data);
  }

  private validateRolesData(data: any) {
    const roleSchema = z.object({
      name: z.string().min(3),
      status: z.enum(['active', 'inactive']),
      details: z.string().optional(),
      access_rights: z.array(z.string()).default([])
    });
    return roleSchema.safeParse(data);
  }



  private validateProductData(data: any) {
    const productSchema = z.object({
      fee_amount: z.number(),
      status: z.enum(['FLAT', 'PERCENTAGE'])
    });
    return productSchema.safeParse(data);
  }



  async createDashboardLogin(data: any) {
    try {

      const { email, first_name, last_name, client_id } = data;
      const password  = await this.randomPassword(6);
      const encryptedPassword  =  CryptoJS.AES.encrypt(password, SECRET_KEY_CLIENT).toString();
      const existingAdmin = await this.selectDataQuery("clients", `client_id = '${client_id}'`);
      if (existingAdmin.length == 0) {
        return this.makeResponse(206, "client doesn't exist");
      }

      const admin: any[] = await this.selectDataQuery("client_logins", `email = '${email}' AND client_id = '${client_id}'`);
      if (admin.length > 0) {
        return this.makeResponse(200, "Email already registered");
      }

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(400, responseData?.message);
      }

      
      const defaultRole: any = await this.defaultRole();
      const adminData = {
        client_id,
        email,
        password: encryptedPassword,
        default_password: true,
        role: defaultRole?.id,
        first_name,
        last_name,
      };
      await this.insertData("client_logins", adminData);
      this.sendEmail("CLIENT_REGISTRATION", email, first_name, password);

      return this.makeResponse(200, "Client access created successfully");
    } catch (error: any) {
      console.error("Error creating admin user:", error);
      return this.makeResponse(500, "email already exists");
    }
  }


  async adminConfirmLogin(email: string, password: string, verificationToken: string) {
    try {

      // 🔎 Check if Admin Exists
      const admin: any[] = await this.selectDataQuery("system_users", `email = '${email}'`);
      if (admin.length === 0) {
        return this.makeResponse(200, "Invalid username/password");
      }

      // 🔐 Verify Password (Decrypt & Compare)
      // const encrypteNewdPassword = CryptoJS.AES.encrypt(password, SECRET_KEY).toString();
      
      // Check if stored password exists and is valid
      if (!admin[0].password || typeof admin[0].password !== 'string') {
        return this.makeResponse(400, "Invalid username/password");
      }

      try {
        const bytes = CryptoJS.AES.decrypt(admin[0].password, SECRET_KEY);
        const originalPassword = bytes.toString(CryptoJS.enc.Utf8);

        if (originalPassword !== password) {
          return this.makeResponse(400, "Invalid username/password");
        }
      } catch (decryptError) {
        console.error("Error decrypting password:", decryptError);
        return this.makeResponse(400, "Invalid username/password");
      }

      const data: any = {};
      data.clientId = admin[0].id;
      data.user_type = 'admin';
      const client: any = await this.userFaAuthAccountStatus({ ...data, user_type: 'admin' })
      if (client.length === 0) {
        return this.makeResponse(400, "Unknown 2fa secret");
      }

      const responseData: any = await TwoFactorAuthHelper.verifySecret(client[0].secret, verificationToken)
      if (!responseData?.status) {
        return this.makeResponse(400, "Invalid 2fa token");
      }

      // 🔑 Generate JWT Token
      const token = jwt.sign({ adminId: admin[0].id, email: admin[0].email }, process.env.JWT_SECRET!, {
        expiresIn: "2h",
      });

      return this.makeResponse(200, "Login successful", { token });
    } catch (error: any) {
      console.error("Error in admin login:", error);
      return this.makeResponse(500, "Error logging in");
    }
  }


  async adminLogin(email: string, password: string) {
    try {

      // 🔎 Check if Admin Exists
      const admin: any[] = await this.selectDataQuery("system_users", `email = '${email}'`);
      if (admin.length === 0) {
        return this.makeResponse(400, "Invalid username/password  ");
      }

      // 🔐 Verify Password (Decrypt & Compare)
      // const encrypteNewdPassword = CryptoJS.AES.encrypt(password, SECRET_KEY).toString();
      
      // Check if stored password exists and is valid
      if (!admin[0].password || typeof admin[0].password !== 'string') {
        return this.makeResponse(400, "Invalid username/password");
      }

      try {
        const bytes = CryptoJS.AES.decrypt(admin[0].password, SECRET_KEY);
        const originalPassword = bytes.toString(CryptoJS.enc.Utf8);
        if (originalPassword !== password) {
          return this.makeResponse(400, "Invalid username/password");
        }
      } catch (decryptError) {
        console.error("Error decrypting password:", decryptError);
        return this.makeResponse(400, "Invalid username/password");
      }

      const data: any = {};
      data.clientId = admin[0].id;
      data.user_type = 'admin';
      const client: any = await this.userFaAuthAccountStatus(data)
      if (client[0]?.status === 'active') {
        return this.makeResponse(200, "Provide a 2fa token to login", { "2fa_status": client[0]?.status });
      }
      const token = jwt.sign({ adminId: admin[0].id, email: admin[0].email }, process.env.JWT_SECRET!, {
        expiresIn: "2h",
      });
      return this.makeResponse(200, "Login successful", { token });
    } catch (error: any) {
      console.error("Error in admin login:", error);
      return this.makeResponse(500, "Error logging in");
    }
  }

  /**
   * ✅ Create Admin User
   */
  async createAdminUser(data: any) {
    try {
      const { email, password, first_name, last_name } = data;

      // 🔎 Check if Email Already Exists
      const existingAdmin = await this.selectDataQuery("system_users", `email = '${email}'`);
      if (existingAdmin.length > 0) {
        return this.makeResponse(401, "Admin user already exists");
      }

      // 🔐 Encrypt Password
      const encryptedPassword = CryptoJS.AES.encrypt(password, SECRET_KEY).toString();

      // 🛠 Insert New Admin User
      const adminData = {
        email,
        password: encryptedPassword,
        role: "admin",
        first_name,
        last_name,
      };
      await this.insertData("system_users", adminData);

      return this.makeResponse(200, "Admin user created successfully");
    } catch (error: any) {
      console.error("Error creating admin user:", error);
      return this.makeResponse(500, "Error creating admin user");
    }
  }



  async getAssetHoldersOld(currency: string) {
    const supply = await new StellarService().getAssetHolders(currency);
    //   const client = await this.callQuery(`select * from supported_currencies `);
    return this.makeResponse(200, "Error fetching stats", supply);
  }


  async getClientCurrencies(clientId: string) {
    //const client = await this.callQuery(`select * from client_currencies c inner join supported_currencies s  where s.asset_code=c.currency and client_id = '${clientId}'`);
    const client = await this.callQuery(`select *  from supported_currencies`);
    return this.makeResponse(200, "Error fetching stats", client);
  }

  async getclientBalance(clientId: any) {
    const currencyInfo: any = await this.getClientCurrencies(clientId);
    const currencies = currencyInfo.data
    for (const currency of currencies) {
      const supply = await new StellarService().getAssetSupply(currency.asset_code);
      currency.balance = supply;
    }
    return this.makeResponse(200, "Client balances fetched successfully", currencies);
  }


  async getAllSystemUsers() {
    try {

      const users = (await this.callQuery(`
        SELECT u.*, r.name as role_name, r.id as role_id, 
         r.details as role_details, r.status as role_status
          FROM system_users u   
           LEFT JOIN roles r ON u.role = r.id
             ORDER BY id DESC`)) as any[];
      if (users.length === 0) {
        return this.makeResponse(401, "System user is not known");
      }

      let usersData: UserData[] = [];
      const defaultRole: any = await this.defaultAdminRole();
      for (const user of users) {

        const acessRights = await this.getRoleAccessRights(user.role);
        let userData: UserData = {
          id: user.id,
          first_name: user.first_name,
          last_name: user.last_name,
          email: user.email,
          role: user.role,
          role_details: {
            id: user.role_id ?? defaultRole?.id,
            name: user.role_name ?? defaultRole?.name,
            details: user.role_details ?? defaultRole?.details,
            status: user.role_status ?? defaultRole?.status,
            access_rights: acessRights
          },
          status: user.status,
          updated_at: user.updated_at,
          created_at: user.created_at
        };
        usersData.push(userData);
      }

      return this.makeResponse(200, "System users fetched successfully", usersData);

    } catch (error: any) {
      console.error("Error fetching system users:", error);
      return this.makeResponse(500, "Error fetching system users");
    }
  }

  async totalClientsStats(data: any) {
    try {

      let conditions: string[] = [];

      if (data?.client_status_type === 'active') {
        conditions.push(`status = '${data.client_status_type}'`);
      }

      if (data?.client_kyc_status === 'active') {
        conditions.push(`kyc_status = '${data.client_kyc_status}'`);
      }

      const qryCondition = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

      const reports: any = await this.callQuery(`
        SELECT COUNT(*) AS total
        FROM clients ${qryCondition}
      `);

      const totalClients = reports?.[0]?.total || 0;

      return this.makeResponse(200, "System total clients fetched successfully", {
        totalClients
      });
    } catch (error: any) {
      console.error("Error fetching system clients:", error);
      return this.makeResponse(500, "Error fetching system clients");
    }
  }


  async totalTransactionsStats() {
    try {

      const reports: any = await this.callQuery(`SELECT COUNT(*) AS total FROM transactions`);
      const totalTransactions = reports?.[0]?.total || 0;

      return this.makeResponse(200, "System total transactions fetched successfully", {
        totalTransactions
      });
    } catch (error: any) {
      console.error("Error fetching system transactions:", error);
      return this.makeResponse(500, "Error fetching system transactions");
    }
  }


  async transactionsStats() {
    try {

      const reports: any = await this.callQuery(`SELECT * FROM transactions ORDER BY created_at DESC`);
      const transactions: any = reports
      return this.makeResponse(200, "System transactions fetched successfully", reports);
    } catch (error: any) {
      console.error("Error fetching system transactions:", error);
      return this.makeResponse(500, "Error fetching system transactions");
    }
  }


  async totalTransactionsFees() {
    try {

      const reports: any = await this.callQuery(`SELECT SUM(fee) AS total
                                                 FROM transactions
                                                 WHERE status = 'SUCCESS'`);
      const totalFee = reports?.[0]?.total || 0;

      return this.makeResponse(200, "System total transaction fees fetched successfully", {
        totalFee
      });

    } catch (error: any) {
      console.error("Error fetching total transaction fees:", error);
      return this.makeResponse(500, "Error fetching total transactionn fees");
    }
  }

  async transactionsFees() {
    try {

      const reports: any = await this.callQuery(`SELECT  id, product_id, trans_type,trans_id,amount,asset_code,currency,
                                                         status,fee,service_name, created_at
                                                           FROM transactions
                                                            WHERE status = 'SUCCESS' ORDER BY created_at DESC`);
      const transactionFee: any = reports;
      return this.makeResponse(200, "System transaction fees fetched successfully", reports);

    } catch (error: any) {
      console.error("Error fetching  transaction fees:", error);
      return this.makeResponse(500, "Error fetching  transactionn fees");
    }
  }

  async reconciliationStats(wallet: string, data: any) {
    try {
      const clientId = wallet;
      /*
      const walletData: any = await this.callQuery(`select * from client_wallets where client_id = '${wallet}'`);
      if (walletData.length === 0) {
        return this.makeResponse(404, "Invalid wallet address", []);
      }
      const clientId = walletData[0].client_id;


      if (wallet === undefined || wallet === "") {
        return this.makeResponse(404, "Provide wallet address");
      }
        */
      const reports: any = await this.callQuery(`SELECT * FROM transactions WHERE client_id = '${clientId}' ORDER BY created_at DESC`);
      return this.makeResponse(200, "Wallet reconciliation reports fetched successfully", reports);

    } catch (error: any) {
      console.error("Error fetching system reconciliation:", error);
      return this.makeResponse(500, "Error fetching system reconciliation");
    }
  }

  async userBalances(clientId: any) {
    console.log(`getStatement`, clientId)

    const currencyInfo: any = await this.getClientCurrencies(clientId);
    const currencies = currencyInfo.data;

    // Get the balances from StellarService
    const balances: any = await new StellarService().getBalance(clientId) || [];

    // For each currency, find a matching balance; if not found, set it to "0"
    for (const currency of currencies) {
      const matchingBalance = balances.find((b: any) => currency.asset_code === b.code);
      currency.balance = matchingBalance ? matchingBalance.balance : "0";
    }
    return this.makeResponse(200, "Client balances fetched successfully", currencies);
  }

  async usersStats(data: any) {
    try {

      const roles = ['verifier', 'approver', 'admin', 'user'];
      const reports: { role: string; count: number }[] = [];
      for (const role of roles) {

        const users = await this.selectDataQuery("system_users", `role = '${role}'`);
        reports.push({ role, count: users?.length || 0 });
      }
      return this.makeResponse(200, "System user reports fetched successfully", reports);

    } catch (error: any) {
      console.error("Error fetching system users:", error);
      return this.makeResponse(500, "Error fetching system users");
    }
  }




  async volumeStats(data: any) {
    try {

      let { start_date, end_date } = data;

      // Determine date range
      let dateFilter = '';
      if (start_date && end_date && start_date !== "" && end_date !== "") {
        const formattedStartDate = await formatDateForMySQL(start_date);
        const formattedEndDate = await formatDateForMySQL(end_date);
        dateFilter = ` AND created_at >= '${formattedStartDate}' AND created_at <= '${formattedEndDate}'`;
        //dateFilter = ` AND created_at BETWEEN '${formattedStartDate}' AND '${formattedEndDate}'`;
      } else {
        const firstDayOfMonth = getFirstDayOfCurrentMonth();
        const today = getCurrentDateMySQL();
        dateFilter = ` AND created_at >= '${firstDayOfMonth}' AND created_at < '${today}'`;
      }

      // SQL query with parameterized values (consider using prepared statements)
      const sql = `
            SELECT 
                currency,
                SUM(CASE WHEN trans_type = 'PUSH' THEN amount ELSE 0 END) AS push_volume,
                SUM(CASE WHEN trans_type = 'PULL' THEN amount ELSE 0 END) AS pull_volume,
                SUM(CASE WHEN trans_type = 'SWAP' THEN amount ELSE 0 END) AS swap_volume,
                SUM(CASE WHEN trans_type = 'BANK_DEPOSIT' THEN amount ELSE 0 END) AS bank_deposit_volume,
                SUM(amount) AS net_volume,
                SUM(ABS(amount)) AS total_movement,
                COUNT(*) AS transaction_count
            FROM 
                transactions
            WHERE 
                trans_type IN ('PUSH', 'PULL', 'SWAP', 'BANK_DEPOSIT')
                AND status = 'SUCCESS'
                ${dateFilter}
            GROUP BY 
                currency
            ORDER BY 
                total_movement DESC`;

      const reports = await this.callQuery(sql);
      return this.makeResponse(200, "System volume reports fetched successfully", {
        reports: reports,
        datePeriod: {
          start: start_date || getFirstDayOfCurrentMonth(),
          end: end_date || getCurrentDateMySQL(),
          filter: dateFilter ? 'custom' : 'default'
        }
      });
    } catch (error) {
      console.error("Error fetching system volume:", error);
      return this.makeResponse(500, "Error fetching system volume");
    }
  }



  async getProfitLiquidityRailNetworkStats() {
    try {

      //const reports = await this.selectDataQuery("transactions", `status = 'SUCCESS'`);
      const reports: any = [{
        created_on: "",
        ex_rate: "",
        transId: "",
        amount: "",
        currency: "",
        spread: "",
        fee: ""
      }]; // await this.callQuery(`SELECT * FROM transactions WHERE status = 'SUCCESS'`);
      return this.makeResponse(200, "System volume reports fetched successfully", reports);
    } catch (error: any) {
      console.error("Error fetching system volume:", error);
      return this.makeResponse(500, "Error fetching system volume");
    }

  }


  async getProfitMudapayStats(data: any) {
    try {
      let { start_date, end_date, trans_type, currency } = data;

      // Determine date range
      let dateFilter = '';
      if (start_date && end_date && start_date !== "" && end_date !== "") {
        const formattedStartDate = await formatDateForMySQL(start_date);
        const formattedEndDate = await formatDateForMySQL(end_date);
        dateFilter = ` AND created_at >= '${formattedStartDate}' AND created_at <= '${formattedEndDate}'`;
        // dateFilter = ` AND created_at BETWEEN '${formattedStartDate}' AND '${formattedEndDate}'`;
      } else {
        const firstDayOfMonth = getFirstDayOfCurrentMonth();
        const firstDayOfNextMonth = getFirstDayOfNextMonth();
        dateFilter = ` AND created_at >= '${firstDayOfMonth}' AND created_at <= '${firstDayOfNextMonth}'`;
      }

      let transTypeFilter = '';
      if (trans_type && trans_type !== "") {
        transTypeFilter = ` AND trans_type = '${trans_type}'`;
      }

      let currencyTypeFilter = '';
      if (currency && currency !== "") {
        currencyTypeFilter = ` AND currency = '${currency}'`;
      }

      // Enhanced query to include fee information and additional fields
      const sql = `
        SELECT 
          trans_type,
          trans_id,
          amount,
          asset_code,
          currency,
          status,
          fee,
          provider_fees,
          created_at
        FROM transactions 
        WHERE status = 'SUCCESS' 
        ${dateFilter} 
        ${transTypeFilter}
        ${currencyTypeFilter}
        ORDER BY created_at DESC
      `;

      const rawReports: any = await this.callQuery(sql);

      // Map and calculate additional fee fields
      const mappedReports: any = rawReports.map((transaction: any) => {
        return {
          ...transaction,
          muda_fees: transaction?.fee ?? "",
          provider_fees: transaction?.provider_fees ?? "",
          profit: Number(transaction?.fee) - Number(transaction?.provider_fees)
        };
      });

      return this.makeResponse(200, "Muda pay profit reports fetched successfully", {
        transactions: mappedReports,
        dateRange: {
          start: start_date || getFirstDayOfCurrentMonth(),
          end: end_date || getFirstDayOfNextMonth()
        },
        filters: {
          trans_type: trans_type || 'All'
        }
      });
    } catch (error: any) {
      console.error("Error fetching Muda pay profit reports:", error);
      return this.makeResponse(500, "Error fetching Muda pay profit reports");
    }
  }



  async profitonTradeStats() {
    try {

      const reports: any = await this.callQuery(`SELECT  id, product_id, trans_type,trans_id,amount,asset_code,currency,
                                                         status,fee,service_name, created_at
                                                           FROM transactions ORDER BY created_at DESC`);
      return this.makeResponse(200, "System profit on trade reports fetched successfully", reports);
    } catch (error: any) {
      console.error("Error fetching system profit:", error);
      return this.makeResponse(500, "Error fetching system profit");
    }
  }



  async twoFaAuthAccountStatus(data: any) {
    try {

      if (data?.clientId === undefined) {
        return this.makeResponse(401, "Unknown user account");
      }
      // console.log("data log >>>", data)

      let dataStatus: any = { status: '' }

      const client: any = await this.userFaAuthAccountStatus({ ...data, user_type: 'admin' })
      // console.log("data log client >>>", client)
      if (client[0]?.status) {
        dataStatus = { status: client[0].status }
      }
      return this.makeResponse(200, "user 2fa status successfully", dataStatus);

    } catch (error: any) {

      console.error("Error creating system user:", error);
      return this.makeResponse(500, "Error creating system user");
    }
  }

  async twoFaAuthAccountCode(data: any) {
    try {

      if (data?.clientId === undefined) {
        return this.makeResponse(200, "Unknown user account");
      }

      const client: any = await this.userFaAuthAccountStatus({ ...data, user_type: 'admin' })
      if (client.lenght > 0) {
        const clientData = { status: client[0].secret }
        return this.makeResponse(200, "Unknown user account", clientData);
      }

      if (client[0]?.secret && client[0]?.secret !== "") {
        return this.makeResponse(200, "User 2fa code created successfully",
          { code: client[0]?.secret, url: client[0]?.qr_code });
      }
      const authCode: any = await TwoFactorAuthHelper.generateSecret({ ...data, user_type: 'admin' });
      const userData = {
        id: uuidv4(),
        user_id: data.clientId,
        secret: authCode?.secret,
        user_type: 'admin',
        qr_code: authCode?.qrCode,
        code: authCode?.data?.data?.hex,
        status: 'pending'
      };
      await this.insertData("user_2fa", userData);
      return this.makeResponse(200, "User user 2fa successfully", { code: authCode?.data?.data?.base32, url: authCode?.qrCode });

    } catch (error: any) {

      console.error("Error creating system user:", error);
      return this.makeResponse(500, "Error generating user 2fa code");
    }
  }

  async twoFaAuthVerify(id: string, data: any) {
    try {

      if (data?.clientId === undefined) {
        return this.makeResponse(401, "Unknown user account");
      }

      const client: any = await this.userFaAuthAccountStatus({ ...data, user_type: 'admin' })
      if (client.length === 0) {
        data = { status: client[0]?.secret ?? "" }
        return this.makeResponse(200, "Unknown 2fa secret");
      }

      const responseData: any = await TwoFactorAuthHelper.verifySecret(client[0].secret, data?.token)
      console.log("responseData  2fa token verify  - ", responseData);
      if (responseData?.status) {
        const userData = {
          status: 'active'
        };
        await this.updateData("user_2fa", `user_id = '${data?.clientId}'`, userData);
        return this.makeResponse(200, "User 2FA verification approved", responseData);
      }

      return this.makeResponse(200, "User 2FA verification failed", responseData);
    } catch (error: any) {

      console.error("Error verifying 2fa code:", error);
      return this.makeResponse(500, "Error verifying 2fa code");
    }
  }



  async twoFaAuthUpdate(id: string, data: any) {
    try {

      if (data?.clientId === undefined) {
        return this.makeResponse(401, "Unknown user account");
      }

      if (!['active', 'inactive'].includes(data.status)) {
        return this.makeResponse(401, `Status must be either 'active' or 'inactive'`);
      }

      const client: any = await this.userFaAuthAccountStatus({ ...data, user_type: 'admin' })
      if (client.lenght === 0) {
        data = { status: client[0].secret }
        return this.makeResponse(200, "Unknown 2fa secret");
      }
      const theData: any = {
        status: data?.status
      }

      const saved: any = await this.updateData("user_2fa", `user_id = '${data?.clientId}' AND user_type = 'admin' AND deleted_at IS NULL`, theData)
      if (data?.status === 'active') {
        return this.makeResponse(200, "User 2fa  has been activated successfully");
      } else {
        return this.makeResponse(200, "User 2fa has been deactivated successfully");
      }
    } catch (error: any) {

      console.error("Error creating system user:", error);
      return this.makeResponse(500, "Error creating system user");
    }
  }



  async addApproveBusinessKyc(client_id: string, data: any) {
    try {

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(400, responseData?.message);
      }

      const checkPair: any[] = await this.selectDataQuery("clients", `client_id = '${client_id}' AND kyc_status = 'verified'`);
      if (checkPair.length > 0) {
        return this.makeResponse(400, "Business kyc is already verified");
      }

      await this.updateData("clients", `client_id = '${client_id}'`, { kyc_status: 'verified'});
      return this.makeResponse(200, "Business kyc approved successfully", {});

    } catch (error: any) {
      return this.makeResponse(500, "Error approving client kyc");
    }  

} 



  /*****
  * 
  *   add business 
  * 
  * */

  async createBusiness(data: any) {
    try {

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(400, responseData?.message);
      }

      if (data?.clientId === undefined) {
        return this.makeResponse(401, "Unknown user account");
      }


      const checkEmail: any[] = await this.selectDataQuery("clients", `contact_email = '${data.contact_email}'`);
      if (checkEmail.length > 0) {
        return this.makeResponse(400, "Client email already taken");
      }
      
      const checkPair_ = await this.selectDataQuery("maker_checker", `status = 'pending' AND JSON_UNQUOTE(JSON_EXTRACT(data_content, '$.contact_email')) = '${data.contact_email}'`);
      if (checkPair_.length > 0) {
        return this.makeResponse(206, "Business with same email already placed for verification");
      }

      const checkContactEmail: any[] = await this.selectDataQuery("client_logins", `email = '${data.contact_email}'`);
      if (checkContactEmail.length > 0) {
        return this.makeResponse(400, "Client contact email already taken");
      }

      const checkPhone: any[] = await this.selectDataQuery("clients", `phone_number = '${data.phone_number}'`);
      if (checkPhone.length > 0) {
        return this.makeResponse(400, "Client phone number already taken");
      }


      const checkName: any[] = await this.selectDataQuery("clients", `business_name = '${data.business_name}'`);
      if (checkName.length > 0) {
        return this.makeResponse(400, "Client business name already taken");
      }

      const client_id = this.generateRandom4DigitNumber()
      const saveData = {
        client_id: client_id,
        business_name: data.business_name,
        contact_email: data.contact_email,
        phone_number: data.phone_number,
        address: data.address,
        contact_person_name: data.contact_person_name,
        contact_phone: data.contact_phone,
        status: "pending"
      };

      const saveData_ = {
        id: uuidv4(),
        data_content: JSON.stringify(saveData),
        entry_type: "add_business",
        status: "pending",
        maker_id: data.clientId,
        created_at: this.getMySQLDateTime(),
        updated_at: this.getMySQLDateTime()
      }
      const result = await this.insertData("maker_checker", saveData_);
      return this.makeResponse(200, "Business added for approval successfully", result);

    } catch (error: any) {

      console.error("Error adding app pair price >>>>>>>>>>:", error);
      return this.makeResponse(500, "Error adding business for approval");
    }
  }


  async addApproveBusiness(id: string, data: any) {
    try {

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(400, responseData?.message);
      }

      const checkPair = await this.selectDataQuery("maker_checker", `id = '${id}'`);
      if (checkPair.length === 0) {
        return this.makeResponse(400, "Request entry not found");
      }

      if (checkPair[0].status !== 'pending') {
        return this.makeResponse(400, "Request entry was already approved/rejected");
      }

      if (String(checkPair[0].maker_id) === String(data.clientId)) {
        return this.makeResponse(400, "A maker cannot approve their own request");
      }


      const dataContent = JSON.parse(checkPair[0].data_content);

      if (data?.status === 'approved') {
        const checkEmail: any[] = await this.selectDataQuery("clients", `contact_email = '${dataContent.contact_email}'`);
        if (checkEmail.length > 0) {
          return this.makeResponse(400, "Client email already taken");
        }

        const checkContactEmail: any[] = await this.selectDataQuery("client_logins", `email = '${dataContent.contact_email}'`);
        if (checkContactEmail.length > 0) {
          return this.makeResponse(400, "Client contact email already taken");
        }

        const checkPhone: any[] = await this.selectDataQuery("clients", `phone_number = '${dataContent.phone_number}'`);
        if (checkPhone.length > 0) {
          return this.makeResponse(400, "Client phone number already taken");
        }

        const checkName: any[] = await this.selectDataQuery("clients", `business_name = '${dataContent.business_name}'`);
        if (checkName.length > 0) {
          return this.makeResponse(400, "Client business name already taken.");
        }
      }

      await this.updateData("maker_checker", `id = '${id}'`, { approved_at: this.getMySQLDateTime(), checker_id: data?.clientId, reason: data?.reason, status: data?.status });
      if (data?.status === 'rejected') {
        return this.makeResponse(200, "Business creation request was rejected");
      }
      const saveData = {
        client_id: dataContent?.client_id,
        business_name: dataContent?.business_name,
        contact_email: dataContent?.contact_email,
        phone_number: dataContent?.phone_number,
        address: dataContent?.address,
        contact_person_name: dataContent?.contact_person_name,
        contact_phone: dataContent?.contact_phone,
        status: "active",
        default_password: true
      };

      const wallet            = await this.createWallet(dataContent?.client_id)
      const clientId          = await this.insertData("clients", saveData);
      const password: any     = await this.randomPassword(6);
      const encryptedPassword = CryptoJS.AES.encrypt(password, SECRET_KEY_CLIENT).toString();
      const defaultRole: any  = await this.defaultRole();
      const adminData = {
        client_id: dataContent?.client_id,
        email: dataContent?.contact_email,
        password: encryptedPassword,
        first_name: dataContent?.contact_person_name.split(" ")[0],
        last_name: dataContent?.contact_person_name.split(" ")[1],
        role: defaultRole?.id,
        status: "active",
        default_password: true
      };
      await this.insertData("client_logins", adminData);
      this.sendEmail("CLIENT_REGISTRATION", dataContent?.contact_email, dataContent?.first_name ?? "", password);

      return this.makeResponse(200, "Business added successfully", clientId);

    } catch (error: any) {
      return this.makeResponse(500, "Error adding business");
    }
  }



  async updateBusiness(id: string, data: any) {
    try {

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(400, responseData?.message);
      }

      const existingAdmin: any[] = await this.selectDataQuery("clients", `client_id = '${id}'`);
      if (existingAdmin.length === 0) {
        return this.makeResponse(401, "Business is not known");
      }

      let saveData: any = {
        business_name: data?.business_name,
        phone_number: data?.phone_number,
        address: data?.address,
        contact_person_name: data?.contact_person_name,
        contact_phone: data?.contact_phone,
        status: data?.status
      };

      await this.updateData("clients", `client_id = '${data.id}'`, saveData);
      return this.makeResponse(200, "Business updated successfully");

    } catch (error: any) {
      console.error("Error Role:", error);
      return this.makeResponse(500, "Error updating business details");
    }
  }


  async updateBusinessAdminDetails(id: string, data: any) {
    try {

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(400, responseData?.message);
      }

      const existingAdmin: any[] = await this.selectDataQuery("client_logins", `id = '${id}'`);
      if (existingAdmin.length === 0) {
        return this.makeResponse(400, "Business admin is not known");
      }

      const existingBusiness: any[] = await this.selectDataQuery("clients", `client_id = '${existingAdmin[0].client_id}'`);
      if (existingBusiness.length === 0) {
        return this.makeResponse(400, "Business is not known");
      }
      
      let saveContactUserData: any = {}
      let password: any;
      if (data?.reset_password && data?.reset_password === true) {
        password = await this.randomPassword(6);
        const encryptedPassword: any = CryptoJS.AES.encrypt(password, SECRET_KEY_CLIENT).toString();
        saveContactUserData.password = encryptedPassword;
        saveContactUserData.default_password = true;
      }
      if (data?.reset_role && data?.reset_role === true) {
        saveContactUserData.role = "admin";
      }
      if (data?.first_name !== "") {
        saveContactUserData.first_name = data?.first_name;
      }
      if (data?.last_name !== "") {
        saveContactUserData.last_name = data?.last_name;
      }
      if (data?.status !== "") {
        saveContactUserData.status = data?.status;
      }

      await this.updateData("client_logins", `id = '${existingAdmin[0].id}'`, saveContactUserData);
      if (data?.reset_password && data?.reset_password === true) {
        this.sendEmail("CLIENT_PASSWORD_CHANGE", existingAdmin[0].email, existingAdmin[0]?.first_name ?? "", password);
      }
      return this.makeResponse(200, "Business admin details updated successfully");

    } catch (error: any) {
      console.error("Error Role:", error);
      return this.makeResponse(500, "Error updating business details");
    }
  }


  async getClients() {
    try {
      const users = await this.selectDataQuery("clients");
      return this.makeResponse(200, "clients", users);
    } catch (error: any) {
      console.error("Error fetching system users:", error);
      return this.makeResponse(500, "Error fetching system users");
    }
  }

  async getClientById(clientId: string) {
    try {
      const client = await this.selectDataQuery("clients", `client_id = '${clientId}'`);
      if (client.length === 0) {
        return this.makeResponse(400, "Client not found");
      }

      return this.makeResponse(200, "Client fetched successfully", client[0]);
    } catch (error: any) {
      console.error("Error fetching client:", error);
      return this.makeResponse(500, "Error fetching client");
    }
  }

  async getBusinessesToBeApproved(data: any) {
    try {

      let currenctStatus: any = "pending";
      if (data?.status) {
        currenctStatus = data?.status;
      }
      const businnesse: any[] = await this.selectDataQuery("maker_checker", `( entry_type = 'add_business' OR entry_type = 'edit_business' ) AND status = '${currenctStatus}' ORDER BY created_at DESC`);
      if (businnesse.length === 0) {
        return this.makeResponse(400, "No business requests");
      }
      const businnesseData: any[] = await Promise.all(
        businnesse.map(async (contentData: any) => {
          const dataContent: any = JSON.parse(contentData.data_content);
          return {
            ...contentData,
            data_content: dataContent
          };
        })
      );
      return this.makeResponse(200, "Business listed successfully", businnesseData);
    } catch (error: any) {
      return this.makeResponse(500, "Error fetching app business requests");
    }
  }





  /*****
  *   add business custom fees
  * */
  async addApproveBusinessFees(id: string, data: any): Promise<any> {
    try {
      const responseData = await this.confirmUser2Fa(data);
      if (!responseData?.status) return this.makeResponse(400, responseData?.message);
      const checkPair = await this.selectDataQuery("maker_checker", `id = '${id}'`);
      if (checkPair.length === 0) return this.makeResponse(400, "Request entry not found");
      if (checkPair[0].status !== 'pending') return this.makeResponse(400, "Request entry already processed");
      if (String(checkPair[0].maker_id) === String(data.clientId)) return this.makeResponse(400, "A maker cannot approve their own request");
  
      await this.updateData("maker_checker", `id = '${id}'`, {
        approved_at: this.getMySQLDateTime(),
        checker_id: data.clientId,
        reason: data.reason,
        status: data.status
      });
  
      if (data.status === 'rejected') return this.makeResponse(400, "Business custom fees creation request was rejected");
  
      const dataContent = JSON.parse(checkPair[0].data_content);
      const saveData = {
        id: dataContent.id,
        client_id: dataContent.client_id,
        product_id: dataContent.product_id,
        fee_type: dataContent.fee_type,
        amount: dataContent.amount,
        active_status: dataContent.active_status
      };
  
      const result = await this.insertData("custome_fees", saveData);
      return this.makeResponse(200, "Business custom fees added successfully", result);
    } catch (error: any) {
      console.error("Error approving business fees:", error);
      return this.makeResponse(500, "Error adding business fees");
    }
  }
  

  async updateBusinessFees(product_id: string, client_id: string, data: any): Promise<any> {
    try {
      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) return this.makeResponse(200, responseData?.message);
      if (!data?.clientId) return this.makeResponse(400, "Unknown user account");
  
      const clientDetails = await this.selectDataQuery("clients", `client_id = '${client_id}'`);
      if (clientDetails.length === 0) return this.makeResponse(400, "Business is not known");
  
      const products = await this.selectDataQuery("products", `product_id = '${product_id}'`);
      if (products.length === 0) return this.makeResponse(400, "Fee product is not known");
  
      if(data.fee_type === "percentage" && Number(data.amount) > 100) return this.makeResponse(400, "Percentage fee cannot be greater than 100");
      if(Number(data.amount) <= 0) return this.makeResponse(400, "Fee cannot be less than 0");

      const fees = await this.selectDataQuery("custome_fees", `client_id = '${client_id}' AND product_id = '${product_id}' AND deleted_at IS NULL ORDER BY created_at DESC LIMIT 1`);
      if (fees.length === 0) {
        const checkPending = await this.selectDataQuery("maker_checker", `entry_type = 'add_business_fees' AND status = 'pending' AND JSON_UNQUOTE(JSON_EXTRACT(data_content, '$.product_id')) = '${product_id}' AND JSON_UNQUOTE(JSON_EXTRACT(data_content, '$.client_id')) = '${client_id}'`);
        if (checkPending.length > 0) return this.makeResponse(206, "Business custom fees already placed for verification");
  
        const saveData = {
          id: uuidv4(),
          client_id: client_id,
          product_id: product_id,
          fee_type: data.fee_type,
          amount: data.amount,
          active_status: data.active_status,

          product_name: products[0].product_name, 
          product_code: products[0].product_code, 
          product_transaction_type: products[0].transaction_type, 
          product_currency: products[0].currency
        };
  
        const saveData_ = {
          id: uuidv4(),
          data_content: JSON.stringify(saveData),
          entry_type: "add_business_fees",
          status: "pending",
          maker_id: data.clientId,
          created_at: this.getMySQLDateTime(),
          updated_at: this.getMySQLDateTime()
        };
  
        const result = await this.insertData("maker_checker", saveData_);
        return this.makeResponse(200, "Business fees added for approval successfully", result);
      }
  
      const feesData = {
        amount: data.amount,
        fee_type: data.fee_type,
        active_status: data.active_status
      };
  
      await this.updateData("custome_fees", `client_id = '${client_id}' AND product_id = '${product_id}'`, feesData);
      return this.makeResponse(200, "Client custom fee updated successfully");
    } catch (error: any) {
      console.error("Error editing business fees:", error);
      return this.makeResponse(500, "Error editing business custom fee");
    }
  }
  

  async deleteBusinessFees(client_id: string, product_id: string, data: any) {
    try {

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) return this.makeResponse(200, responseData?.message);
  
      const clientDetails = await this.selectDataQuery("clients", `client_id = '${client_id}'`);
      if (clientDetails.length === 0) return this.makeResponse(400, "Business is not known");
  
      const products = await this.selectDataQuery("products", `product_id = '${product_id}'`);
      if (products.length === 0) return this.makeResponse(400, "Fee product is not known");
  
      const feesData = {
        deleted_at: this.getMySQLDateTime()
      };
      await this.updateData("custome_fees", `client_id = '${client_id}' AND product_id = '${product_id}'`, feesData);
      return this.makeResponse(200, "Client custom fee deleted successfully");

    } catch (error: any) {

      console.error("Error editing business custom fee:", error);
      return this.makeResponse(500, "Error editing business custom fee");
    }
  }

  async getAllBusinessFees(client_id: string, data: any) {
    try {

      const fees: any[] = await this.selectDataQuery("products", ``);
      const feesData = await Promise.all(
        fees.map(async (fee: any) => {
          let custom_fee: any =  await this.selectDataQuery("custome_fees", `product_id = '${fee.product_id}' AND client_id = '${client_id}' AND deleted_at IS NULL order by created_at desc`)
          return {
            ...fee,
            fee_amount: custom_fee[0]?.amount ?? fee?.fee_amount,
            custom_tag: (custom_fee[0]?.amount && custom_fee[0]?.amount !== "")? "custom":"",
            fee_type:   custom_fee[0]?.fee_type ?? fee?.fee_type,
            custom_fee: custom_fee[0]
          };
        }));

      return this.makeResponse(200, "business transaction fees fetch", feesData);

    } catch (error: any) {

      console.error("Error getting all business fees:", error);
      return this.makeResponse(500, "Error getting all business fees");
    }
  }

  async getBusinessFee(client_id: string, product_id: string, data: any) {
    try {

      const fees: any[] = await this.selectDataQuery("products", `product_id = '${product_id}'`);
      const feesData = await Promise.all(
        fees.map(async (fee: any) => {
          let custom_fee: any =  await this.selectDataQuery("custome_fees", `product_id = '${fee.product_id}' AND client_id = '${client_id}' AND deleted_at IS NULL order by created_at desc`)
          return {
            ...fee,
            fee_amount: custom_fee[0]?.amount ?? fee?.fee_amount,
            custom_tag: (custom_fee[0]?.amount && custom_fee[0]?.amount !== "")? "custom":"",
            fee_type:   custom_fee[0]?.fee_type ?? fee?.fee_type,
            custom_fee: custom_fee[0]
          };
        }));
      
      return this.makeResponse(200, "business transaction fee fetch", feesData);
    } catch (error: any) {
      console.error("Error getting a business fees:", error);
      return this.makeResponse(500, "Error getting a business fees");
    }
  }


  async getBusinessCustomFeesToBeApproved(data: any) {
    try {

      let currenctStatus: any = "pending";
      // if(data?.status){
      //   currenctStatus = data?.status;
      // }
      let pairPrices: any = "";
      if(data?.client_id === "" || data?.client_id === undefined){
        pairPrices = (await this.callQuery(`
            SELECT *
            FROM maker_checker
            WHERE ( maker_checker.entry_type = 'add_business_fees' OR maker_checker.entry_type = 'edit_business_fees' ) AND maker_checker.status = '${currenctStatus}'
          `));
      } else {
       pairPrices = (await this.callQuery(`
            SELECT *  
            FROM maker_checker
            WHERE ( maker_checker.entry_type = 'add_business_fees' OR maker_checker.entry_type = 'edit_business_fees' ) AND maker_checker.status = '${currenctStatus}' AND JSON_UNQUOTE(JSON_EXTRACT(data_content, '$.client_id')) = '${data?.client_id}'
          `));
      }
      
      if (pairPrices.length === 0) {
        return this.makeResponse(400, "No business fees requests", pairPrices);
      }
      const pairPricesData: any[] = await Promise.all(
        pairPrices.map(async (pairPrice: any) => {
          const dataContent: any = JSON.parse(pairPrice.data_content);
          return {
            ...pairPrice,
            data_content: dataContent
          };
        })
      );
      return this.makeResponse(200, "Business fees listed successfully", pairPricesData);
    } catch (error: any) {
      return this.makeResponse(500, "Error fetching app business fees");
    }
  }



  /*****
  * 
  *   update the users section 
  * 
  * */

  async createSystemUser(data: any) {
    try {

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(400, responseData?.message);
      }

      const { email, first_name, last_name, user_role } = data;
      const userRole: any[] = await this.selectDataQuery("roles", `id = '${user_role}'`);
      if (userRole.length === 0) {
        return this.makeResponse(401, "Unknown provided user role");
      }

      const existingAdmin: any[] = await this.selectDataQuery("system_users", `email = '${email}'`);
      if (existingAdmin.length > 0) {
        return this.makeResponse(401, "System user already exists");
      }

      const checkPair_ = await this.selectDataQuery("maker_checker", `status = 'pending' AND JSON_UNQUOTE(JSON_EXTRACT(data_content, '$.email')) = '${email}'`);
      if (checkPair_.length > 0) {
        return this.makeResponse(206, "Admin account already placed for verification");
      }

      const saveData = {
        id: uuidv4(),
        email: email,
        password: "",
        role: user_role,
        status: 'inactive',
        first_name: first_name,
        last_name: last_name
      };

      const saveData_ = {
        id: uuidv4(),
        data_content: JSON.stringify(saveData),
        entry_type: "add_admin",
        status: "pending",
        maker_id: data.clientId,
        created_at: this.getMySQLDateTime(),
        updated_at: this.getMySQLDateTime()
      }
      const result = await this.insertData("maker_checker", saveData_);
      return this.makeResponse(200, "User account placed for approval", result);
    } catch (error: any) {
      console.log("Error roles", error);
      return this.makeResponse(500, "Error adding account");
    }
  }

  async createApproveSystemUser(id: string, data: any) {
    try {

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(400, responseData?.message);
      }

      const checkPair = await this.selectDataQuery("maker_checker", `id = '${id}'`);
      if (checkPair.length === 0) {
        return this.makeResponse(400, "Request entry not found");
      }

      if (checkPair[0].status !== 'pending') {
        return this.makeResponse(400, "Request entry was already approved/rejected");
      }

      if (String(checkPair[0].maker_id) === String(data.clientId)) {
        return this.makeResponse(400, "A maker cannot approve their own request");
      }

      await this.updateData("maker_checker", `id = '${id}'`, { approved_at: this.getMySQLDateTime(), checker_id: data?.clientId, reason: data?.reason, status: data?.status });
      if (data?.status === 'rejected') {
        return this.makeResponse(200, "System user creation request was rejected");
      }
      const dataContent = JSON.parse(checkPair[0].data_content);
      const password: any = await this.randomPassword(6);

      const encryptedPassword: any = CryptoJS.AES.encrypt(`${password}`, SECRET_KEY).toString();

      const userData = {
        email: dataContent.email,
        password: encryptedPassword,
        role: dataContent.role,
        status: "active", //dataContent.status,
        first_name: dataContent.first_name,
        last_name: dataContent.last_name
      };
      const response: any = await this.insertData("system_users", userData);
      this.sendEmail("ADMIN_REGISTRATION", dataContent?.email, dataContent?.first_name, password);

      return this.makeResponse(200, "User created successfully");
    } catch (error: any) {

      console.log("errro on approving content", error);
      return this.makeResponse(500, "Error adding app system users");
    }
  }



  async updateSystemUser(id: string, data: any) {
    try {

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(400, responseData?.message);
      }

      const { first_name, last_name } = data;

      const existingAdmin: any[] = await this.selectDataQuery("system_users", `id = '${id}'`);
      if (existingAdmin.length === 0) {
        return this.makeResponse(401, "System user is not known");
      }

      const userRole: any[] = await this.selectDataQuery("roles", `id = '${data?.user_role}'`);
      if (userRole.length === 0) {
        return this.makeResponse(401, "Unknown provided user role");
      }


      let saveData: any = {
        updated_at: this.getMySQLDateTime()
      };

      let password: any;
      if (data?.reset_password && data?.reset_password === true) {
        password = await this.randomPassword(6);
        const encryptedPassword: any = CryptoJS.AES.encrypt(password, SECRET_KEY).toString();
        saveData.password = encryptedPassword;
      }

      if (data?.status && data?.status !== "") {
        saveData.status = data.status;
      }

      if (data?.first_name && data?.first_name !== "") {
        saveData.first_name = data.first_name;
      }

      if (data?.last_name && data?.last_name !== "") {
        saveData.last_name = data.last_name;
      }

      if (data?.user_role && data?.user_role !== "") {
        saveData.role = data.user_role;
      }


      await this.updateData("system_users", `id = '${id}'`, saveData);
      if (data?.reset_password && data?.reset_password === true) {
          this.sendEmail("USER_PASSWORD_CHANGE", existingAdmin[0].email, existingAdmin[0]?.first_name ?? "", password);
      }
      return this.makeResponse(200, "User account updated successfully");
    } catch (error: any) {


      console.error("Error Role:", error);
      return this.makeResponse(500, "Error updating app user");
    }
  }

  async updateApprovalSystemUser(id: string, data: any) {
    try {

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(400, responseData?.message);
      }

      const checkPair = await this.selectDataQuery("maker_checker", `id = '${id}'`);
      if (checkPair.length === 0) {
        return this.makeResponse(400, "Request entry not found");
      }

      if (checkPair[0].status !== 'pending') {
        return this.makeResponse(400, "Request entry was already approved/rejected");
      }

      if (String(checkPair[0].maker_id) === String(data.clientId) ) {
        return this.makeResponse(400, "A maker cannot approve their own request");
      }

      await this.updateData("maker_checker", `id = '${id}'`, { approved_at: this.getMySQLDateTime(), checker_id: data.clientId, reason: data?.reason, status: data.status });
      if (data?.status === 'rejected') {
        return this.makeResponse(200, "System user updating request was rejected");
      }
      const dataContent: any = JSON.parse(checkPair[0].data_content);


      let saveData: any = {
        name: dataContent.name,
        details: dataContent.details,
        status: dataContent.status,
        updated_at: dataContent.updated_at
      };

      let password: any;
      if (dataContent?.reset_password && dataContent?.reset_password === true) {
        password = await this.randomPassword(6);
        const encryptedPassword: any = CryptoJS.AES.encrypt(password, SECRET_KEY).toString();
        saveData.password = encryptedPassword;
      }

      if (dataContent?.role) {
        saveData.role = dataContent.role;
      }

      if (dataContent?.status) {
        saveData.status = dataContent.status;
      }

      await this.updateData("system_users", `id = '${dataContent.id}'`, saveData);
      if (dataContent?.reset_password && dataContent?.reset_password === true) {
        this.sendEmail("CLIENT_REGISTRATION", dataContent.email, dataContent.first_name, dataContent.password);
      }
      return this.makeResponse(200, "User account updated successfully");

    } catch (error: any) {
      console.error("Error adding role:", error);
      return this.makeResponse(500, "Error updating role");
    }
  }

  async getSystemUsersToBeApproved(data: any) {
    try {

      let currenctStatus: any = "pending";
      if (data?.status) {
        currenctStatus = data?.status;
      }
      const contentData: any[] = await this.selectDataQuery("maker_checker", `( entry_type = 'add_admin' OR entry_type = 'edit_admin' ) AND status = '${currenctStatus}'`);
      if (contentData.length === 0) {
        return this.makeResponse(401, "No  user requests", []);
      }
      const contentDataData: any[] = await Promise.all(
        contentData.map(async (pairPrice: any) => {
          const dataContent: any = JSON.parse(pairPrice.data_content);
          return {
            ...pairPrice,
            data_content: dataContent
          };
        })
      );
      return this.makeResponse(200, "Users listed successfully", contentDataData);
    } catch (error: any) {
      return this.makeResponse(500, "Error fetching app roles");
    }
  }


  async userProfile(data: any) {
    try {

      if (data?.clientId === undefined) {
        return this.makeResponse(401, "Unknown user account");
      }
      const user = (await this.callQuery(`SELECT u.*, r.name as role_name, 
          r.id as role_id, r.details as role_details, r.status as role_status
          FROM system_users u   
          LEFT JOIN roles r ON u.role = r.id
          WHERE u.id = '${data?.clientId}'`)) as any[];
      if (user.length === 0) {
        return this.makeResponse(401, "System user is not known");
      }

      delete user[0].password;
      let acessRights: any[] = [];
      // defult user role view rights

      if(user[0].role === "0000-0000-0000-0003"){
        
        const acessRightsAll: any = await this.callQuery(`
                                        SELECT r.*
                                        FROM access_rights r WHERE (r.type = 'admin' OR r.type IS NULL) AND r.deleted_at IS NULL
                                        ORDER BY r.created_at DESC
                                      `);
        const allowedRightsIds: any = ["d1d46ab5-41c1-11f0-bd21-16243d6fb08b","d1d46d88-41c1-11f0-bd21-16243d6fb08b","d1b17217-41c1-11f0-bd21-16243d6fb08b","d17176b6-41c1-11f0-bd21-16243d6fb08b","d149ac93-41c1-11f0-bd21-16243d6fb08b"]
        // add a new key to acessRightsAll that is access_rights_status
        acessRights = acessRightsAll.filter((role: any) => allowedRightsIds.includes(role.id));
        acessRights.forEach((role: any) => {
          role.access_rights_status     = "active";
          role.access_rights_status_id  = "";
          role.role_id                  = role.id;
        });
        
        const defaultRole: any = await this.defaultAdminRole();
        user[0].role_id = defaultRole?.id;
        user[0].role_name = defaultRole?.name;
        user[0].role_details = defaultRole?.details;
        user[0].role_status = defaultRole?.status;

      } else {
        acessRights = await this.getRoleAccessRights(user[0].role);
      }


      let userData: UserData = {
        id: user[0].id,
        first_name: user[0].first_name,
        last_name: user[0].last_name,
        email: user[0].email,
        role: user[0].role,
        role_details: {
          id: user[0].role_id,
          name: user[0].role_name,
          details: user[0].role_details,
          status: user[0].role_status,
          access_rights: acessRights
        },
        status: user[0].status,
        updated_at: user[0].updated_at,
        created_at: user[0].created_at
      };
      return this.makeResponse(200, "user details", userData);
    } catch (error: any) {

      console.error("Error fetching user profile:", error);
      return this.makeResponse(500, "Error fetching user profile");
    }
  }

  async userDetails(id: any) {
    try {

      const user = (await this.callQuery(`SELECT u.*, r.name as role_name,
            r.id as role_id, r.details as role_details, r.status as role_status
            FROM system_users u   
            LEFT JOIN roles r ON u.role = r.id
            WHERE u.id = '${id}'`)) as any[];
      if (user.length === 0) {
        return this.makeResponse(401, "System user is not known");
      }

      delete user[0].password;
      const acessRights = await this.getRoleAccessRights(user[0].role);

      let userData: UserData = {
        id: user[0].id,
        first_name: user[0].first_name,
        last_name: user[0].last_name,
        email: user[0].email,
        role: user[0].role,
        role_details: {
          id: user[0].role_id,
          name: user[0].role_name,
          details: user[0].role_details,
          status: user[0].role_status,
          access_rights: acessRights
        },
        status: user[0].status,
        updated_at: user[0].updated_at,
        created_at: user[0].created_at
      };

      return this.makeResponse(200, "user details", userData);

    } catch (error: any) {

      console.error("Error fetching user profile:", error);
      return this.makeResponse(500, "Error fetching user profile");
    }
  }

  async resetPassword(data: any) {
    try {

      const { clientId, current_password, new_password } = data;
      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(400, responseData?.message);
      }


      if (!clientId || !current_password || !new_password) {
        return this.makeResponse(400, "Account current and new passwords are required");
      }

      // Verify current password
      const admin = await this.selectDataQuery("system_users", `id = '${clientId}'`);
      
      // Check if stored password exists and is valid
      if (!admin[0]?.password || typeof admin[0].password !== 'string') {
        return this.makeResponse(400, "Current password is incorrect");
      }

      const bytes = CryptoJS.AES.decrypt(admin[0].password, SECRET_KEY);
      const originalPassword = bytes.toString(CryptoJS.enc.Utf8);
      if (originalPassword !== current_password) {
        return this.makeResponse(400, "Current password is incorrect");
      }

      // Update with new password
      const newPassword_ = CryptoJS.AES.encrypt(new_password, SECRET_KEY).toString();
      await this.updateData("system_users", `id = '${clientId}'`, { password: newPassword_ });
      return this.makeResponse(200, "Password updated successfully");

    } catch (error: any) {
      console.error("Error resetting password:", error);
      return this.makeResponse(500, "Error resetting password");
    }
  }

  async changeProfile(data: any) {
    try {

      const { clientId, first_name, last_name } = data;
      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(400, responseData?.message);
      }

      if (!clientId || (!first_name && !last_name)) {
        return this.makeResponse(400, "Missing required fields");
      }
      const admin = await this.selectDataQuery("system_users", `id = '${clientId}'`);
      if (admin.length === 0) {
        return this.makeResponse(404, "User not found");
      }

      const updateData: any = {};
      if (first_name) updateData.first_name = first_name;
      if (last_name) updateData.last_name = last_name;
      await this.updateData("system_users", `id = '${clientId}'`, updateData);
      return this.makeResponse(200, "Profile updated successfully");
    } catch (error: any) {
      console.error("Error updating profile:", error);
      return this.makeResponse(500, "Error updating profile");
    }
  }


  /*****
   * 
   *   update the roles section 
   * */

  async addRole(data: any) {
    try {

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(400, responseData?.message);
      }
      const VALIDATION = this.validateRolesData(data);
      if (!VALIDATION.success) {
        return this.makeResponse(400, "Invalid data", VALIDATION.error.errors);
      }
      const savedType: any = await this.callQuery(`SELECT COUNT(*) as total FROM roles r WHERE r.name = '${data?.name}' AND r.deleted_at IS NULL`);
      if (savedType[0].total > 0) {
        return this.makeResponse(401, "Role name already exists");
      }

      const saveData = {
        id: uuidv4(),
        name: data?.name,
        details: data?.details || "",
        status: data?.status || 'active',
        created_at: this.getMySQLDateTime(),
        access_rights: data.access_right
      };

      const saveData_ = {
        id: uuidv4(),
        data_content: JSON.stringify(saveData),
        entry_type: "add_role",
        status: "pending",
        maker_id: data.clientId,
        created_at: this.getMySQLDateTime(),
        updated_at: this.getMySQLDateTime()
      }
      const result = await this.insertData("maker_checker", saveData_);
      return this.makeResponse(200, "Role placed for approval successfully", result);
    } catch (error: any) {
      console.log("Error roles", error);
      return this.makeResponse(500, "Error adding role");
    }
  }

  async addApproveRole(id: string, data: any) {
    try {

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(400, responseData?.message);
      }

      const checkPair = await this.selectDataQuery("maker_checker", `id = '${id}'`);
      if (checkPair.length === 0) {
        return this.makeResponse(400, "Request entry not found");
      }

      if (checkPair[0].status !== 'pending') {
        return this.makeResponse(400, "Request entry was already approved/rejected");
      }

      if (String(checkPair[0].maker_id) === String(data.clientId) ) {
        return this.makeResponse(400, "A maker cannot approve their own request");
      }

      await this.updateData("maker_checker", `id = '${id}'`, { approved_at: this.getMySQLDateTime(), checker_id: data?.clientId, reason: data?.reason, status: data?.status });
      if (data?.status === 'rejected') {
        return this.makeResponse(200, "Role creation request was rejected");
      }

      const dataContent = JSON.parse(checkPair[0].data_content);
      const saveData = {
        id: dataContent.id,
        name: dataContent?.name,
        details: dataContent?.details || "",
        status: dataContent?.status || 'active',
        created_at: dataContent.created_at
      };

      const result = await this.insertData("roles", saveData);
      await this.updateRoleAccessRights(dataContent.id, dataContent.access_rights);

      return this.makeResponse(200, "Role added successfully", result);
    } catch (error: any) {
      console.log("ROLE Approving issues", error)
      return this.makeResponse(500, "Error approving role");
    }
  }


  // private async updateRoleAccessRights(roleId: string, accessRights: string[]) {

  //   const EXISTING_RIGHTS: any = await this.callQuery(`SELECT id, access_right_id FROM role_access_rights  WHERE role_id = '${roleId}' AND deleted_at IS NULL`);
  //   console.log("EXISTING_RIGHTS", EXISTING_RIGHTS);
  //   const EXISTING_IDS = EXISTING_RIGHTS.map((right: any) => right.access_right_id);
  //   const NEW_IDS = accessRights;

  //   const IDS_TO_DELETE = EXISTING_IDS.filter((id: string) => !NEW_IDS.includes(id));
  //   const IDS_TO_ADD = NEW_IDS.filter((id: string) => !EXISTING_IDS.includes(id));
  //   console.log("EXISTING_RIGHTS TO DELETE", IDS_TO_DELETE);
  //   console.log("EXISTING_RIGHTS TO ADD", IDS_TO_ADD);

  //   if (IDS_TO_DELETE.length > 0) {
  //     await this.updateData(
  //       "role_access_rights",
  //       `role_id = '${roleId}' AND access_right_id IN ('${IDS_TO_DELETE.join("','")}')`,
  //       { deleted_at: this.getMySQLDateTime() }
  //     );
  //   }

  //   // finally add the rights
  //   for (const ACCESS_RIGHT_ID of IDS_TO_ADD) {
  //     const ROLE_ACCESS_RIGHT_DATA = {
  //       id: uuidv4(),
  //       role_id: roleId,
  //       access_right_id: ACCESS_RIGHT_ID,
  //       created_at: this.getMySQLDateTime()
  //     };
  //     await this.insertData("role_access_rights", ROLE_ACCESS_RIGHT_DATA);
  //   }
  // }


  async updateRole(id: string, data: any) {
    try {

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(400, responseData?.message);
      }

      const VALIDATION = this.validateRolesData(data);
      if (!VALIDATION.success) {
        return this.makeResponse(400, "Invalid data", VALIDATION.error.errors);
      }

      const savedType: any = await this.callQuery(`
        SELECT COUNT(*) as total
        FROM roles r 
        WHERE r.id != '${id}' AND r.name = '${data?.name}' AND r.deleted_at IS NULL
      `);

      if (savedType[0].total > 0) {
        return this.makeResponse(401, "Role name already exists");
      }

      const saveData = {
        name: data.name,
        details: data.details,
        status: data.status,
        updated_at: data.updated_at
      };
      await this.updateData("roles", `id = '${id}'`, saveData);
      await this.updateRoleAccessRights(id, data.access_right);
      return this.makeResponse(200, "Role updated successfully");

    } catch (error: any) {
      console.error("Error Role:", error);
      return this.makeResponse(500, "Error updating app Role");
    }
  }


  async deleteRole(id: string, data: any) {
    try {

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(400, responseData?.message);
      }
      const saveData = {
        updated_at: data.updated_at,
        deleted_at: this.getMySQLDateTime()
      };

      await this.updateData("roles", `id = '${id}'`, saveData);
      await this.updateRoleAccessRights(id, []);
      const defaultRole: any = await this.defaultAdminRole();
      await this.updateData("system_users", `role = '${id}'`, { role: defaultRole.id });
      return this.makeResponse(200, "Role deleted successfully");

    } catch (error: any) {
      console.error("Error Role:", error);
      return this.makeResponse(500, "Error updating app Role");
    }
  }


  async updateApprovalRole(id: string, data: any) {
    try {

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(400, responseData?.message);
      }

      const checkPair = await this.selectDataQuery("maker_checker", `id = '${id}'`);
      if (checkPair.length === 0) {
        return this.makeResponse(400, "Request entry not found");
      }

      if (checkPair[0].status !== 'pending') {
        return this.makeResponse(400, "Request entry was already approved/rejected");
      }

      if (String(checkPair[0].maker_id) === String(data.clientId) ) {
        return this.makeResponse(400, "A maker cannot approve their own request");
      }

      await this.updateData("maker_checker", `id = '${id}'`, { approved_at: this.getMySQLDateTime(), checker_id: data.clientId, reason: data?.reason, status: data.status });
      if (data?.status === 'rejected') {
        return this.makeResponse(200, "Role updating request was rejected");
      }
      const dataContent: any = JSON.parse(checkPair[0].data_content);
      const saveData = {
        name: dataContent.name,
        details: dataContent.details,
        status: dataContent.status,
        updated_at: dataContent.updated_at
      };
      await this.updateData("roles", `id = '${dataContent.id}'`, saveData);
      await this.updateRoleAccessRights(dataContent.id, dataContent.access_rights);

      return this.makeResponse(200, "Role updated successfully");

    } catch (error: any) {
      console.error("Error adding role:", error);
      return this.makeResponse(500, "Error updating role");
    }
  }

  async getRoleAccessRights(roleId: string): Promise<any[]> {

    let accessRights: any[] = [];
    if(roleId === "0000-0000-0000-0003"){

      const acessRightsAll: any = await this.callQuery(`
                                      SELECT r.*
                                      FROM access_rights r WHERE (r.type = 'admin' OR r.type IS NULL) AND r.deleted_at IS NULL
                                      ORDER BY r.created_at DESC
                                    `);
      const allowedRightsIds: any = ["d1d46ab5-41c1-11f0-bd21-16243d6fb08b","d1d46d88-41c1-11f0-bd21-16243d6fb08b","d1b17217-41c1-11f0-bd21-16243d6fb08b","d17176b6-41c1-11f0-bd21-16243d6fb08b","d149ac93-41c1-11f0-bd21-16243d6fb08b"]
      // add a new key to acessRightsAll that is access_rights_status
      accessRights = acessRightsAll.filter((role: any) => allowedRightsIds.includes(role.id));
      accessRights.map((role: any) => {
                                            role.access_rights_status     = "active";
                                            role.access_rights_status_id  = "";
                                            role.role_id                  = role.id;
                                      });                              
    } else {
      accessRights = (await this.callQuery(` SELECT 
                                            rar.id as access_rights_status_id, 
                                            ar.id as role_id,  
                                            ar.name as name, 
                                            ar.status as access_rights_status
                                            FROM access_rights ar
                                            INNER JOIN role_access_rights rar ON ar.id = rar.access_right_id
                                            WHERE rar.role_id = '${roleId}'
                                            AND rar.status = 'active'  
                                            AND ar.deleted_at IS NULL 
                                            AND rar.deleted_at IS NULL
                                          `)) as any[];
    }
    
    return accessRights;
  }

  async getRoles() {
    try {
      const savedType: any = await this.callQuery(`
        SELECT r.*
        FROM roles r
        WHERE r.deleted_at IS NULL
        ORDER BY r.created_at DESC
      `);

      if (savedType.length > 0) {
        for (const role of savedType) {
          const accessRights = await this.callQuery(`
             SELECT rar.access_right_id as role_access_rights_id, 
                   rar.role_id as role_id, 
                   ar.name as name, 
                   ar.status as access_rights_status
            FROM access_rights ar
            INNER JOIN role_access_rights rar ON ar.id = rar.access_right_id
            WHERE rar.role_id = '${role.id}'
            AND rar.status = 'active'  
            AND ar.deleted_at IS NULL 
            AND rar.deleted_at IS NULL
          `);
          role.access_rights = accessRights;
        }
      }
      return this.makeResponse(200, "Roles fetched successfully", savedType);
    } catch (error: any) {
      return this.makeResponse(500, "Error fetching roles");
    }
  }

  async getRolesAccessRights() {
    try {
      const savedType: any = await this.callQuery(`
        SELECT r.*
        FROM access_rights r WHERE (r.type = 'admin' OR r.type IS NULL) AND r.deleted_at IS NULL
        ORDER BY r.created_at DESC
      `);
      return this.makeResponse(200, "Role access rights fetched successfully", savedType);
    } catch (error: any) {
      return this.makeResponse(500, "Error fetching role access rights");
    }
  }

  async getRole(id: string) {
    try {
      const savedType: any = await this.callQuery(`
        SELECT r.*
        FROM roles r
        WHERE r.id = '${id}' AND 
        r.deleted_at IS NULL
        ORDER BY r.created_at DESC
      `);

      if (savedType.length === 0) {
        return this.makeResponse(404, "Role not found");
      }

      const accessRights = await this.callQuery(`
         SELECT rar.access_right_id as role_access_rights_id, 
                   rar.role_id as role_id, 
                   ar.name as name, 
                   ar.status as access_rights_status
            FROM access_rights ar
            INNER JOIN role_access_rights rar ON ar.id = rar.access_right_id
            WHERE rar.role_id = '${savedType[0].id}'
            AND rar.status = 'active'  
            AND ar.deleted_at IS NULL 
            AND rar.deleted_at IS NULL
      `);
      savedType[0].access_rights = accessRights;

      return this.makeResponse(200, "Role fetched successfully", savedType[0]);
    } catch (error: any) {
      return this.makeResponse(500, "Error fetching role");
    }
  }

  async getRolesToBeApproved(data: any) {
    try {

      let currenctStatus: any = "pending";
      if (data?.status) {
        currenctStatus = data?.status;
      }
      const contentData: any[] = await this.selectDataQuery("maker_checker", `( entry_type = 'add_role' OR entry_type = 'edit_role' ) AND status = '${currenctStatus}'`);
      if (contentData.length === 0) {
        return this.makeResponse(404, "No roles requests");
      }
      const contentDataData: any[] = await Promise.all(
        contentData.map(async (pairPrice: any) => {
          const dataContent: any = JSON.parse(pairPrice.data_content);
          return {
            ...pairPrice,
            data_content: dataContent
          };
        })
      );
      return this.makeResponse(200, "Roles listed successfully", contentDataData);
    } catch (error: any) {
      return this.makeResponse(500, "Error fetching app roles");
    }
  }






  /**
   * 
   *   update the products section 
   * */
  async updateProduct(id: string, data: any) {
    try {

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(400, responseData?.message);
      }

      //check if product exists
      const product = await this.selectDataQuery("products", `product_id = '${id}'`);
      if (product.length === 0) {
        return this.makeResponse(404, "Product not found");
      }

      const saveData = {
        fee_type: data.fee_type,
        fee_amount: data.fee_amount
      };
      await this.updateData("products", `product_id  = '${id}'`, saveData);
      return this.makeResponse(200, "Product updated  successfully");

    } catch (error: any) {
      console.error("Error adding app pair price >>>>>>>>>>:", error);
      return this.makeResponse(500, "Error adding app pair price");
    }
  }


  async updateApproveProduct(id: string, data: any) {
    try {

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(400, responseData?.message);
      }

      const product = await this.selectDataQuery("products", `product_id = '${id}'`);
      if (product.length === 0) {
        return this.makeResponse(404, "Product not found");
      }

      const checkPair = await this.selectDataQuery("maker_checker", `id = '${id}'`);
      if (checkPair.length > 0) {
        return this.makeResponse(400, "Request entry not found");
      }

      if (checkPair[0].status !== 'pending') {
        return this.makeResponse(400, "Request entry was already approved/rejected");
      }

      if (String(checkPair[0].maker_id) === String(data.clientId) ) {
        return this.makeResponse(400, "A maker cannot approve their own request");
      }

      await this.updateData("maker_checker", `id = '${id}'`, { approved_at: this.getMySQLDateTime(), checker_id: data?.clientId, reason: data?.reason, status: data?.status });
      if (data?.status === 'rejected') {
        return this.makeResponse(200, "Product updating request was rejected");
      }
      const dataContent = JSON.parse(checkPair[0].data_content);
      const saveData = {
        fee_type: dataContent.fee_type,
        fee_amount: dataContent.fee_amount
      };
      await this.updateData("products", `id = '${dataContent.id}'`, saveData);
      return this.makeResponse(200, "Product updated  successfully");
    } catch (error: any) {
      return this.makeResponse(500, "Error updating product");
    }
  }

  async getProductsToBeApproved(data: any) {
    try {

      let currenctStatus: any = "pending";
      if (data?.status) {
        currenctStatus = data?.status;
      }
      const responseData: any[] = await this.selectDataQuery("maker_checker", `( entry_type = 'add_product' OR entry_type = 'edit_product' ) AND status = '${currenctStatus}'`);
      if (responseData.length === 0) {
        return this.makeResponse(404, "No products requests");
      }
      const pairPricesData: any[] = await Promise.all(
        responseData.map(async (data: any) => {
          const dataContent: any = JSON.parse(data.data_content);
          return {
            ...data,
            data_content: dataContent
          };
        })
      );
      return this.makeResponse(200, "Products listed successfully", pairPricesData);
    } catch (error: any) {
      return this.makeResponse(500, "Error fetching app products");
    }
  }


  async getProducts() {
    try {
      const products = await this.selectDataQuery("products", ``);
      return this.makeResponse(200, "Products fetched successfully", products);
    } catch (error: any) {
      return this.makeResponse(500, "Error fetching products");
    }
  }

  async getProduct(id: string) {
    try {
      const product = await this.selectDataQuery("products", `product_id = '${id}'`);
      if (product.length === 0) {
        return this.makeResponse(404, "Product not found");
      }
      return this.makeResponse(200, "Product fetched successfully", product[0]);
    } catch (error: any) {
      return this.makeResponse(500, "Error fetching product");
    }
  }



  /// rates section 
  async addAppPairPrices(data: any) {
    try {

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(400, responseData?.message);
      }

      //ckeck og pair exists
      const checkPair = await this.selectDataQuery("exchange_rates", `base_currency = '${data.base_currency}' AND quote_currency = '${data.quote_currency}'`);
      if (checkPair.length > 0) {
        return this.makeResponse(206, "Pair already exists");
      }

      const saveData = {
        id: uuidv4(),
        base_currency: data.base_currency,
        quote_currency: data.quote_currency,
        created_at: this.getMySQLDateTime(),
        pair: `${data.base_currency}/${data.quote_currency}`,
        hasCrypto: false,
        referencePrice: "",
        markup: data.markup,
        markdown: data.markdown
      };

      const saveData_ = {
        id: uuidv4(),
        data_content: JSON.stringify(saveData),
        entry_type: "add_exchange_rate",
        status: "pending",
        maker_id: data.clientId,
        created_at: this.getMySQLDateTime(),
        updated_at: this.getMySQLDateTime()
      }
      const result = await this.insertData("maker_checker", saveData_);
      return this.makeResponse(200, "Exchange pair price added for approval successfully", result);

    } catch (error: any) {
      console.error("Error adding app pair price >>>>>>>>>>:", error);
      return this.makeResponse(500, "Error adding app pair price");
    }
  }

  async addAppApprovePairPrices(id: string, data: any) {
    try {

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(206, responseData?.message);
      }

      const checkPair = await this.selectDataQuery("maker_checker", `id = '${id}'`);
      if (checkPair.length === 0) {
        return this.makeResponse(206, "Request entry not found");
      }

      if (checkPair[0].status !== 'pending') {
        return this.makeResponse(206, "Request entry was already approved/rejected");
      }

      if (checkPair[0].maker_id === data.clientId) {
        return this.makeResponse(206, "A maker cannot approve their own request");
      }

      await this.updateData("maker_checker", `id = '${id}'`, { approved_at: this.getMySQLDateTime(), checker_id: data?.clientId, reason: data?.reason, status: data?.status });
      if (data?.status === 'rejected') {
        return this.makeResponse(200, "Pair price creation request was rejected");
      }
      const dataContent = JSON.parse(checkPair[0].data_content);
      const saveData = {
        id: dataContent.id,
        base_currency: dataContent.base_currency,
        quote_currency: dataContent.quote_currency,
        created_at: dataContent.created_at,
        pair: dataContent.pair,
        hasCrypto: dataContent.hasCrypto,
        referencePrice: dataContent.referencePrice,
        markup: dataContent.markup,
        markdown: dataContent.markdown
      };

      const result = await this.insertData("exchange_rates", saveData);
      return this.makeResponse(200, "Exchange pair price added successfully", result);
    } catch (error: any) {
      return this.makeResponse(500, "Error adding app pair price");
    }
  }

  async updateAppPairPrices(id: string, data: any) {
    try {

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(206, responseData?.message);
      }

      const prices = await this.selectDataQuery("exchange_rates", `id = '${id}'`);
      if (prices.length === 0) {
        return this.makeResponse(206, "Exchange pair price not found");
      }

      const saveData = {
        hasCrypto: data.hasCrypto,
        referencePrice: data.referencePrice,
        markup: data.markup,
        markdown: data.markdown,
        updated_at: this.getMySQLDateTime()
      };
      await this.updateData("exchange_rates", `id = '${id}'`, saveData);
      return this.makeResponse(200, "Exchange pair price updated successfully");

    } catch (error: any) {
      console.error("Error adding app pair price:", error);
      return this.makeResponse(500, "Error updating app pair price");
    }
  }

  async updateApprovalAppPairPrices(id: string, data: any) {
    try {

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(400, responseData?.message);
      }

      const checkPair = await this.selectDataQuery("maker_checker", `id = '${id}'`);
      if (checkPair.length === 0) {
        return this.makeResponse(400, "Request entry not found");
      }

      if (checkPair[0].status !== 'pending') {
        return this.makeResponse(400, "Request entry was already approved/rejected");
      }

      if (String(checkPair[0].maker_id) === String(data.clientId)) {
        return this.makeResponse(400, "A maker cannot approve their own request");
      }


      await this.updateData("maker_checker", `id = '${id}'`, { approved_at: this.getMySQLDateTime(), checker_id: data.clientId, reason: data?.reason, status: data.status });
      if (data?.status === 'rejected') {
        return this.makeResponse(200, "Pair price updating request was rejected");
      }
      const dataContent: any = JSON.parse(checkPair[0].data_content);

      const saveData = {
        hasCrypto: dataContent.hasCrypto,
        referencePrice: dataContent.referencePrice,
        markup: dataContent.markup,
        markdown: dataContent.markdown,
        updated_at: this.getMySQLDateTime()
      };
      await this.updateData("exchange_rates", `id = '${dataContent.id}'`, saveData);
      return this.makeResponse(200, "Exchange pair price updated successfully");
    } catch (error: any) {
      console.error("Error adding app pair price:", error);
      return this.makeResponse(500, "Error updating app pair price");
    }
  }

  async getPairPricesToBeApproved(data: any) {
    try {

      let currenctStatus: any = "pending";
      if (data?.status) {
        currenctStatus = data?.status;
      }
      const pairPrices: any[] = await this.selectDataQuery("maker_checker", `( entry_type = 'add_exchange_rate' OR entry_type = 'edit_exchange_rate' ) AND status = '${currenctStatus}'`);
      if (pairPrices.length === 0) {
        return this.makeResponse(404, "No exchange pair price requests");
      }
      const pairPricesData: any[] = await Promise.all(
        pairPrices.map(async (pairPrice: any) => {
          const dataContent: any = JSON.parse(pairPrice.data_content);
          return {
            ...pairPrice,
            data_content: dataContent
          };
        })
      );
      return this.makeResponse(200, "Pair prices listed successfully", pairPricesData);
    } catch (error: any) {
      return this.makeResponse(500, "Error fetching app pair price");
    }
  }


  async getAppPairPrices() {
    try {
      const prices: any = (await this.callQuery(`
        SELECT id, enabled, hasCrypto, referencePrice, base_currency, 
               quote_currency, pair, markup, markdown, created_at, updated_at 
        FROM exchange_rates 
        WHERE enabled = 1
      `));
      const pricesData = await Promise.all(
        prices.map(async (currencyPair: any) => {
          let fBestExchangeRate;
          let rateDetails: any = {};

          rateDetails = await ratesService.getRates({ quote: currencyPair.base_currency, base: currencyPair?.quote_currency });
          fBestExchangeRate = (rateDetails?.status) ? 1 / Number(rateDetails?.price) : "";

          return {
            ...currencyPair,
            current_exchange_rate: fBestExchangeRate
          };
        })
      );
      return this.makeResponse(200, "Exchange pair prices fetched successfully", pricesData);
    } catch (error: any) {
      console.error("Error fetching app pair price:", error);
      return this.makeResponse(500, "Error fetching app pair prices");
    }
  }


  async getAppPairPriceById(id: string) {
    try {
      const prices = await this.selectDataQuery("exchange_rates", `id = '${id}'`);
      if (prices.length === 0) {
        return this.makeResponse(404, "Exchange pair price not found");
      }
      delete prices[0].updatedBy
      let rateDetails: any = "";
      let fBestExchangeRate: any = "";
      rateDetails = await ratesService.getRates({ quote: prices[0].base_currency, base: prices[0].quote_currency });
      fBestExchangeRate = (rateDetails?.status) ? 1 / Number(rateDetails?.price) : "";
      prices[0].current_exchange_rate = fBestExchangeRate
      return this.makeResponse(200, "Exchange pair price fetched successfully", prices[0]);
    } catch (error: any) {
      console.error("Error fetching app pair price:", error);
      return this.makeResponse(500, "Error fetching app pair price");
    }
  }


  async getBaseCurrencyRate(base_currency: string) {
    try{
    const prices: any = (await this.callQuery(`
      SELECT id, enabled, hasCrypto, referencePrice, base_currency, 
             quote_currency, pair, markup, markdown, created_at, updated_at 
      FROM exchange_rates 
      WHERE enabled = 1 AND base_currency = '${base_currency}'
    `));
    const pricesData = await Promise.all(
      prices.map(async (currencyPair: any) => {
        let fBestExchangeRate;
        let rateDetails: any = {};

        rateDetails = await ratesService.getRates({ quote: currencyPair.base_currency, base: currencyPair?.quote_currency });
        fBestExchangeRate = (rateDetails?.status) ? 1 / Number(rateDetails?.price) : "";

        return {
          ...currencyPair,
          current_exchange_rate: fBestExchangeRate
        };
      })
    );
    return this.makeResponse(200, "Exchange pair prices fetched successfully", pricesData);
  } catch (error: any) {
    console.error("Error fetching app pair price:", error);
    return this.makeResponse(500, "Error fetching app pair prices");
  }
  }


  async getPairsExchangeAmount(data: any) {
    try{
      const prices: any = (await this.callQuery(`
      SELECT id, enabled, hasCrypto, referencePrice, base_currency,  quote_currency, pair, markup, markdown, created_at, updated_at 
      FROM exchange_rates WHERE enabled = 1 AND base_currency = '${data.base_currency}' AND quote_currency = '${data.quote_currency}'
    `));
    
    if (prices.length === 0) {
      return this.makeResponse(404, "Exchange pair not found");
    }


    if(prices[0].updated_at) {
      const updatedAt = new Date(prices[0].updated_at);
      const now = new Date();
      const diffInMinutes = (now.getTime() - updatedAt.getTime()) / (1000 * 60);
      
      if(diffInMinutes < 1) {
        const cachedRate = prices[0].referencePrice || "";
        const fBestExchangeRate = cachedRate ? Number(cachedRate) : "";
        const fBestExchangeRateAmount = fBestExchangeRate ? (fBestExchangeRate * data.amount) : "";
        
        const result = {
          rate: fBestExchangeRate,
          exchangedAmount: fBestExchangeRateAmount,
          source: "cached"
        };
        return this.makeResponse(200, "Exchange pair amount calculated successfully (cached)", result);
      }
    }

    const rateDetails: any = await ratesService.getRates({ quote: prices[0].base_currency, base: prices[0]?.quote_currency });
    console.log("rateDetails", rateDetails);
    const fBestExchangeRate: any = (rateDetails?.status) ? Number(rateDetails?.price) : "";
    const fBestExchangeRateAmount: any = (fBestExchangeRate * data.amount);

    const result = {
      rate: fBestExchangeRate,
      exchangedAmount: fBestExchangeRateAmount
    };
    
    return this.makeResponse(200, "Exchange pair amount calculated successfully", result);
  } catch (error: any) {
    console.error("Error fetching app pair price:", error);
    return this.makeResponse(500, "Error fetching app pair prices");
  }
  }



  // banks
  async getASystemBanks(id: any) {
    try {

      const banks = await this.selectDataQuery("banks", `id = '${id}'`);
      if (banks.length === 0) {
        return this.makeResponse(404, "Bank not found");
      }
      return this.makeResponse(200, "Bank fetched successfully", banks[0]);
    } catch (error: any) {
      console.error("Error fetching Bank:", error);
      return this.makeResponse(500, "Error fetching Bank");
    }
  }

  async getSystemBanks(data: any) {
    try {
      const banks = await (await this.callQuery(`select * from banks order by created_at DESC `));
      return this.makeResponse(200, "Banks fetched successfully", banks);
    } catch (error: any) {
      console.error("Error fetching Banks:", error);
      return this.makeResponse(500, "Error fetching app Banks");
    }
  }

  async addSystemBanks(data: any) {
    try {

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(400, responseData?.message);
      }

      //check og pair exists
      const checkPair = await this.selectDataQuery("banks", `account_number = '${data.account_number}'`);
      if (checkPair.length > 0) {
        return this.makeResponse(206, "Account number already registered");
      }

      const checkPair_ = await this.selectDataQuery("maker_checker", `status = 'pending' AND JSON_UNQUOTE(JSON_EXTRACT(data_content, '$.account_number')) = '${data.account_number}'`);
      if (checkPair_.length > 0) {
        return this.makeResponse(206, "Account number already placed for verification");
      }

      const saveData = {
                          id: uuidv4(),
                          bank_name: data.bank_name,
                          branch_name: data.branch_name ?? "",
                          branch_address: data.branch_address ?? "",
                          account_name: data.account_name,
                          account_number: data.account_number,
                          swift_code: data.swift_code,
                          country: data.country,
                          currency: data.currency,
                          beneficiary_address: data.beneficiary_address ?? "",
                          created_at: this.getMySQLDateTime()
                      };

      const saveData_ = {
        id: uuidv4(),
        data_content: JSON.stringify(saveData),
        entry_type: "add_payment_methods",
        status: "pending",
        maker_id: data.clientId,
        created_at: this.getMySQLDateTime(),
        updated_at: this.getMySQLDateTime()
      }
      const result = await this.insertData("maker_checker", saveData_);
      return this.makeResponse(200, "Exchange payment method added for approval successfully", result);

    } catch (error: any) {
      console.error("Error adding payment method  >>>>>>>>>>:", error);
      return this.makeResponse(500, "Error adding payment method ");
    }
  }

  async editApprovalPaymentMthods(id: string, data: any) {

    try {

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        // return this.makeResponse(206, responseData?.message);
      }

      const checkPair = await this.selectDataQuery("maker_checker", `id = '${id}'`);
      if (checkPair.length === 0) {
        return this.makeResponse(206, "Request entry not found");
      }

      if (checkPair[0].status !== 'pending') {
        return this.makeResponse(206, "Request entry was already approved/rejected");
      }

      if (String(checkPair[0].maker_id) === String(data.clientId)) {
       return this.makeResponse(206, "A maker cannot approve their own request");
      }

      await this.updateData("maker_checker", `id = '${id}'`, { approved_at: this.getMySQLDateTime(), checker_id: data?.clientId, reason: data?.reason, status: data?.status });
      if (data?.status === 'rejected') {
        return this.makeResponse(200, "Payment method request was rejected");
      }
      const dataContent = JSON.parse(checkPair[0].data_content);

      const checkPair_ = await this.selectDataQuery("banks", `account_number = '${dataContent.account_number}'`);
      if (checkPair_.length > 0) {
        return this.makeResponse(206, "Bank account number already registered");
      }


      const saveData = {
                          id: dataContent.id,
                          bank_name: dataContent.bank_name,
                          branch_name: dataContent.branch_name ?? "",
                          branch_address: dataContent.branch_address ?? "",
                          account_name: dataContent.account_name,
                          account_number: dataContent.account_number,
                          swift_code: dataContent.swift_code,
                          country: dataContent.country,
                          currency: dataContent.currency,
                          beneficiary_address: dataContent.beneficiary_address ?? "",
                          created_at: dataContent.created_at
                      };

      const result = await this.insertData("banks", saveData);
      return this.makeResponse(200, "Payment method added successfully", result);
    } catch (error: any) {
      return this.makeResponse(500, "Error adding bank account");
    }
  }

  async getAllPendingPaymentMethods(data: any) {
    try {

      let currenctStatus: any = "pending";
      if (data?.status) {
        currenctStatus = data?.status;
      }
      const paymentMtds: any[] = await this.selectDataQuery("maker_checker", `( entry_type = 'add_payment_methods' OR entry_type = 'edit_payment_methods' ) AND status = '${currenctStatus}'`);
      if (paymentMtds.length === 0) {
        return this.makeResponse(404, "No payment method requests");
      }
      const paymentMtdsData: any[] = await Promise.all(
        paymentMtds.map(async (paymentMtds: any) => {
          const dataContent: any = JSON.parse(paymentMtds.data_content);
          return {
            ...paymentMtds,
            data_content: dataContent
          };
        })
      );
      return this.makeResponse(200, "Payment method requests listed successfully", paymentMtdsData);
    } catch (error: any) {
      return this.makeResponse(500, "Error fetching app  payment method ");
    }
  }

  async updateSystemBanks(id: string, data: any) {
    try {


      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(206, responseData?.message);
      }

      const existBank = await this.selectDataQuery("banks", `id = '${id}'`);
      if (existBank.length === 0) {
        return this.makeResponse(400, "Bank account not found");
      }

      const checkPair = await this.selectDataQuery("banks", `id != '${id}' AND account_number = '${data.account_number}'`);
      if (checkPair.length > 0) {
        return this.makeResponse(206, "Bank account number already registered");
      }
      const saveData = {
        bank_name: data.bank_name,
        branch_name: data.branch_name ?? "",
        branch_address: data.branch_address ?? "",
        account_name: data.account_name,
        account_number: data.account_number,
        swift_code: data.swift_code,
        country: data.country,
        currency: data.currency,
        beneficiary_address: data.beneficiary_address ?? ""
      };
      const updateBank = await this.updateData("banks", `id = '${id}'`, saveData);
      return this.makeResponse(200, `Bank account updated successfully`);

    } catch (error: any) {
      console.error("Error adding bank account:", error);
      return this.makeResponse(500, "Error updating bank account");
    }
  }

}


