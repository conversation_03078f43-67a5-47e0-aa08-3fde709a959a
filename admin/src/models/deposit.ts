import Model from "../helpers/model";
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from "../helpers/2fa.helper";
import EmailSender from "../helpers/email.helper";
import { v4 as uuidv4 } from "uuid";
import jwt from "jsonwebtoken";
import CryptoJ<PERSON> from "crypto-js";
import { z } from "zod";
import { LiquidityRailService } from "../services/liquidityrail.service";
import RatesService from "../helpers/exchangerates.helper";

const SECRET_KEY = ""; // process.env.SECRET_KEY || "your-secret-key";
const sendmail = new EmailSender()
const ratesService = new RatesService();
const liquidityRailService = new LiquidityRailService();


export default class Deposit extends Model {
  private readonly Deposit_TABLE = 'custome_fees';

  constructor() {
    super();
  }

  async syncBalances() {
    try{
    const transactions: any = await this.callQuery("SELECT * FROM transactions WHERE status = 'FAILED' AND message IS NULL ORDER BY id desc LIMIT 500");
    for (const transaction of transactions) {
      console.log("Processing transaction", transaction.trans_id);
       const WebhookData = await this.selectDataQuery(" webhook_logs", `trans_id = '${transaction.trans_id}'`);
       console.log("WebhookData", WebhookData);
      if (WebhookData.length > 0) {
        const webhook_data = WebhookData[0].webhook_data
        console.log("webhook_data", webhook_data);

       const dataObj = JSON.parse(webhook_data);
        console.log("dataObj", dataObj);
        const { message: webhookMessage, statusCode } = dataObj;
        if (statusCode == 400) {
          console.log("Updating transaction", transaction.trans_id, "with message", webhookMessage);
          await this.updateData("transactions", `trans_id = '${transaction.trans_id}'`, { message: `${webhookMessage}` });
        }
      }
    }
    } catch (error: any) {
      console.error("Error syncing balances:", error);
    }
  }

  async issueTokens(data: any) {
    return this.makeResponse(409, "SUCCESS", data);
  }

  private getMySQLDateTime() {
    return new Date().toISOString().slice(0, 19).replace('T', ' ');
  }

  async depositRequest(data: any) {
    try {
      const { client_id, from, to, reference_id, currency, narration, pin, amount } = data;
      // const fromAsset = "c" + currency;
      const fromAsset = currency;
      console.log(fromAsset)
      // Check if the client exists
      const client = await this.selectDataQuery("clients", `client_id = '${client_id}'`);
      if (client.length === 0) {
        return this.makeResponse(404, "Client not found");
      }
      if (reference_id.length > 20 || reference_id.length < 12) {
        return this.makeResponse(400, "Reference ID must be between 12 and 20 characters");
      }

      const responseData: any = await this.confirmUser2Fa(data);
      if (!responseData?.status) {
        return this.makeResponse(401, responseData?.message);
      }

      const refId = this.getRandomString()
      const transactionData: any = {
        reference_id: reference_id,
        validation_id: refId,
        client_id: client_id,
        product_id: 1000,
        trans_id: refId,
        amount: amount,
        req_amount: amount,
        trans_type: "BANK_DEPOSIT",
        asset_code: fromAsset,
        sender_account: fromAsset,
        currency,
        receiver_account: client_id,
        memo: narration,
        status: "pending",
      };

      await this.insertData("transactions", transactionData);
      return this.makeResponse(200, "Deposit request sent successfully", { reference_id: refId });

    } catch (error: any) {
      console.error("Error requesting deposit:", error);
      return this.makeResponse(500, "Error requesting deposit");
    }
  }




}
