import express, { Request, Response } from "express";
import Accounts from "../models/accounts";
import { JWTMiddleware } from "../helpers/jwt.middleware";


const router = express.Router();
const accounts = new Accounts();

const applyJWTConditionally = (req: Request, res: Response, next: any) => {
  JWTMiddleware.verifyTokenAccess(req, res, next);
  // next()
};

const saveApiLog = (req: Request, res: Response, next: any) => {
  // thirdPartyHandler.saveApiLog(req.body, req.ip || "");  // Moved to wallet service
  next();
}

router.post("/generate-api-key", applyJWTConditionally, saveApiLog, generateApiKey);
router.get("/client/:id", applyJWTConditionally, getClientById);
router.get("/profile", applyJWTConditionally, profile);
router.post("/login", userLogin);
router.post("/login/confirm", adminConfirmLogin);

router.get("/getapikeys", applyJWTConditionally, getapikeys);
router.get("/currencies", applyJWTConditionally, getCurrencies);
router.post("/reset-password", resetPassword);
router.get("/get-stats", applyJWTConditionally, getStats);
router.get("/balances", applyJWTConditionally, getClientBalance);
router.get("/supported-currencies", applyJWTConditionally, getClientCurrencies);
router.get("/supported-chains", applyJWTConditionally, getClientChains);
router.post("/addCurrency", applyJWTConditionally, addClientCurrency);
router.post("/request-swap", applyJWTConditionally, requestSwap);
router.get("/webhooks", applyJWTConditionally, webhooks);
router.post("/send-webhook", sendWebhook);
router.post("/addWebhook", applyJWTConditionally, saveApiLog, addWebhook);
router.post("/delete-webhook", applyJWTConditionally, saveApiLog, deleteHook);
router.post("/oauth/token", saveApiLog, OAauthLogin);
router.post("/delete-api-key", applyJWTConditionally, saveApiLog, deleteApiKey);

router.post("/updateAccountStatus", applyJWTConditionally, saveApiLog, updateAccountStatus);
router.post("/changepassword", applyJWTConditionally, changepassword);
router.get("/paymentMethods", applyJWTConditionally, paymentMethods);
router.get("/getWallet/:id", getWallet);
router.get("/rails/transactions/:id", applyJWTConditionally, getRailsTransactions);
router.post('/oauth/resetPasswordRequest', resetPasswordRequest);
router.post('/oauth/resetPassword', resetPassword);

router.get("/ip-whitelist", applyJWTConditionally, saveApiLog, IpWhitelist);
router.post("/add-ip-whitelist", applyJWTConditionally, saveApiLog, addIpWhitelist);
router.post("/remove-ip", applyJWTConditionally, saveApiLog, removeIpWhitelist);

router.get("/dashboard-logins", applyJWTConditionally, getDashboardLogins);
router.get("/dashboard-logins/:id", applyJWTConditionally, deleteDashboardLogin);
router.post("/dashboard-user", applyJWTConditionally, createDashboardLogin);
router.post("/send-token", applyJWTConditionally, sendUserToken);
router.get("/validate-token/:id", applyJWTConditionally, validateToken);

//2fa
router.get("/users/2fa/code", applyJWTConditionally, get2FaCode);
router.get("/users/2fa/status", applyJWTConditionally, get2FaStatus);
router.post("/users/2fa/verify/code", applyJWTConditionally, verify2FaCode);
router.put("/users/2fa/update", applyJWTConditionally, updating2FaCode);
router.get("/banks", applyJWTConditionally, banks);

router.get("/wallet/addresses", applyJWTConditionally, getUserAddresses);
router.get("/wallet/whitelist", applyJWTConditionally, getUserWhitelist);
router.post("/wallet/remove-whitelist", applyJWTConditionally, removeUserWhitelist);
router.post("/wallet/add-whitelist", applyJWTConditionally, addUserWhitelist);


// pending approval transactions
router.get("/pending-approval", applyJWTConditionally, getPendingApproval);
router.post("/payment/reject", applyJWTConditionally, rejectTransactionPayment);
router.post("/payment/approve", applyJWTConditionally, approveTransactionPayment);

// the rates
router.get("/pair/rates/:base_currency", applyJWTConditionally, baseCurrencyRate);
router.post("/pair/price/rate", applyJWTConditionally, PairExchangeAmount);

//roles
router.get("/roles", applyJWTConditionally, getRoles);
router.get("/roles/access/rights", applyJWTConditionally, getRolesAccessRights);
router.get("/roles/:id", applyJWTConditionally, getARole);
router.post("/roles", applyJWTConditionally, addRole);
router.put("/roles/:id", applyJWTConditionally, editRole);
router.delete("/roles/:id", applyJWTConditionally, deleteRole);

// teams 
router.get("/teams", applyJWTConditionally, getTeams);
router.get("/teams/:id", applyJWTConditionally, getATeamMember);
router.post("/teams", applyJWTConditionally, addATeamMember);
router.put("/teams/:id", applyJWTConditionally, editATeamMember);
router.delete("/teams/:id", applyJWTConditionally, deleteMember);
router.get("/version", version);

// router.get("/business-wallets", applyJWTConditionally, getBusinessWallet);

async function baseCurrencyRate(req: Request, res: Response) {
  try {

    const { base_currency } = req.params;
    const result = await accounts.getBaseCurrencyRate(base_currency);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}


async function PairExchangeAmount(req: Request, res: Response) {
  try {
    const result = await accounts.getPairsExchangeAmount(req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}


async function version(req: Request, res: Response) {
  try {
    const result = { version: "1.2.1" };
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function banks(req: Request, res: Response) {
  try {
    const currency = req.query.currency || '';
    const result = await accounts.banks(currency);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}

async function getDashboardLogins(req: Request, res: Response) {
  try {
    const result = await accounts.getDashboardLogins(req.body.clientId);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function resetDashboardLogin(req: Request, res: Response) {
  try {
    const result = await accounts.resetDashboardLogin(req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function deleteDashboardLogin(req: Request, res: Response) {
  try {
    const result = await accounts.deleteDashboardLogin(req.params.id);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function createDashboardLogin(req: Request, res: Response) {
  try {
    const result = await accounts.createDashboardLogin(req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function sendUserToken(req: Request, res: Response) {
  try {
    const result = await accounts.sendUserToken(req.body.userId);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function validateToken(req: Request, res: Response) {
  try {
    const result = await accounts.validateUserToken(req.params.id, req.body.userId);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}
async function IpWhitelist(req: Request, res: Response) {
  try {
    const result = await accounts.IpWhitelist(req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function removeIpWhitelist(req: Request, res: Response) {
  try {
    const result = await accounts.removeIpWhitelist(req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}
async function addIpWhitelist(req: Request, res: Response) {
  try {
    const result = await accounts.addIpWhitelist(req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}




async function updateAccountStatus(req: Request, res: Response) {
  try {
    const result = await accounts.updateAccountStatus(req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}
async function deleteApiKey(req: Request, res: Response) {
  try {
    const result = await accounts.deleteApiKey(req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}
async function deleteHook(req: Request, res: Response) {
  try {
    const result = await accounts.deleteHook(req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function addWebhook(req: Request, res: Response) {
  try {
    const result = await accounts.addWebhook(req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function sendWebhook(req: Request, res: Response) {

  try {
    const result = await accounts.sendWebhookUrl(req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function webhooks(req: Request, res: Response) {
  try {
    const result = await accounts.webhooks(req.body.clientId);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}


async function requestSwap(req: Request, res: Response) {
  try {
    const result = await accounts.requestSwap(req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function getClientBalance(req: Request, res: Response) {
  try {
    const result = await accounts.getclientBalance(req.body.clientId);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function getClientCurrencies(req: Request, res: Response) {
  try {
    const result = await accounts.getClientCurrencies(req.body.clientId);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function getClientChains(req: Request, res: Response) {
  try {
    const result = await accounts.getClientChains(req.body.clientId);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}
async function addClientCurrency(req: Request, res: Response) {
  try {
    const { currency } = req.body;
    if (!currency) {
      return res.status(400).json({ message: "Currency is required" });
    }

    const result = await accounts.addClientCurrency(req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}
async function getStats(req: Request, res: Response) {
  try {
    const data = req.query;  
    const result = await accounts.getStats(req.body.clientId, data);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}


async function getCurrencies(req: Request, res: Response) {
  try {
    const result = await accounts.getCurrencies();
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}



async function changepassword(req: Request, res: Response) {
  try {
    const result = await accounts.changepassword(req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}



async function getapikeys(req: Request, res: Response) {
  try {
    const result = await accounts.getapikeys(req.body.clientId);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function OAauthLogin(req: Request, res: Response) {
  try {
    const result = await accounts.OAauthLogin(req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function profile(req: Request, res: Response) {
  try {
    const result = await accounts.getClientById(req.body.clientId, req.body.userId);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}
async function getClientById(req: Request, res: Response) {
  try {
    const result = await accounts.getClientById(req.params.id, req.body.userId);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}


// ✅ Generate API Key (PATCH)
async function generateApiKey(req: Request, res: Response) {
  try {
    console.log(req.body)


    const result = await accounts.generateApiKeys(req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

// ✅ Generate API Key (PATCH)
async function paymentMethods(req: Request, res: Response) {
  try {
    const result = await accounts.paymentMethods();
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function getRailsTransactions(req: Request, res: Response) {
  try {
    const result = await accounts.getRailsTransactions(req.params.id);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function getWallet(req: Request, res: Response) {
  try {
    const result = await accounts.getWallet(req.params.id);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function resendOTP(req: Request, res: Response) {
  try {
    const result = await accounts.resetPasswordRequest(req.body);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}
async function resetPasswordRequest(req: Request, res: Response) {
  try {
    const result = await accounts.resetPasswordRequest(req.body);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}

async function resetPassword(req: Request, res: Response) {
  try {
    const result = await accounts.resetPassword(req.body);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }

}



async function get2FaStatus(req: Request, res: Response) {
  try {

    const data: any = req.body;
    const result: any = await accounts.twoFaAuthAccountStatus(data);
    console.log("status code", result)
    return res.status(200).json(result);
  } catch (error: any) {
    console.log("routing error", error)
    return res.status(500).json({ message: "Server error", error });
  }
}

async function get2FaCode(req: Request, res: Response) {
  try {
    const data: any = req.body;
    const result: any = await accounts.twoFaAuthAccountCode(data);
    return res.status(200).json(result);
  } catch (error: any) {
    return res.status(500).json({ message: "Server error", error });
  }
}

async function verify2FaCode(req: Request, res: Response) {
  try {

    const data: any = req.body;
    const { id }: any = req.params;
    const result: any = await accounts.twoFaAuthVerify(id, data);
    return res.status(200).json(result);
  } catch (error: any) {
    return res.status(500).json({ message: "Server error", error });
  }
}

async function updating2FaCode(req: Request, res: Response) {
  try {

    const data: any = req.body;
    const { id }: any = req.params;
    const result: any = await accounts.twoFaAuthUpdate(id, data);
    return res.status(200).json(result);
  } catch (error: any) {
    return res.status(500).json({ message: "Server error", error });
  }
}


async function confirmLogin(req: Request, res: Response) {
  try {

    const { email, password, token } = req.body;
    if (!email || !password) {
      return res.status(400).json({ message: "Email and password are required" });
    }

    if (!token) {
      return res.status(400).json({ message: "Verification token is  required" });
    }

    const result = await accounts.userConfirmLogin(email, password, token);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

///-------------------------
async function userLogin(req: Request, res: Response) {
  try {
    const { email, password } = req.body;
    if (!email || !password) {
      return res.status(400).json({ message: "Email and password are required" });
    }

    const result = await accounts.userLogin(email, password);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function adminConfirmLogin(req: Request, res: Response) {
  try {

    const { email, password, token } = req.body;
    if (!email || !password) {
      return res.status(400).json({ message: "Email and password are required" });
    }

    if (!token) {
      return res.status(400).json({ message: "verification token is  required" });
    }

    const result = await accounts.userConfirmLogin(email, password, token);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function getUserAddresses(req: Request, res: Response) {
  try {
    const result = await accounts.getUserAddresses(req.body.clientId);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}
async function getUserWhitelist(req: Request, res: Response) {
  try {
    const result = await accounts.getUserWhitelist(req.body.clientId);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function addUserWhitelist(req: Request, res: Response) {
  try {
    const result = await accounts.addUserWhitelist(req.body);
    res.status(200).json(result);
  } catch (error: any) {
    console.log("error", error)
    res.status(500).json({ message: "Server error", error });
  }
}
async function removeUserWhitelist(req: Request, res: Response) {
  try {
    const result = await accounts.removeUserWhitelist(req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}





// access rights clients
async function getRolesAccessRights(req: Request, res: Response) {
  try {
    const result = await accounts.getRolesAccessRights();
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function getRoles(req: Request, res: Response) {
  try {
    const data = req.body;
    const result = await accounts.getRoles(data);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function getARole(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const data = req.body;
    const result = await accounts.getRole(id, data);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function addRole(req: Request, res: Response) {
  try {
    const result = await accounts.addRole(req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function editRole(req: Request, res: Response) {
  try {

    const { id } = req.params;
    const result = await accounts.updateRole(id, req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function deleteRole(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const result = await accounts.deleteRole(id);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}



async function getTeams(req: Request, res: Response) {
  try {
    const result = await accounts.getTeams(req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function getATeamMember(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const result = await accounts.getATeamMember(id, req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function addATeamMember(req: Request, res: Response) {
  try {
    const result = await accounts.addATeamMember(req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function editATeamMember(req: Request, res: Response) {
  try {

    const { id } = req.params;
    const result = await accounts.editATeamMember(id, req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function deleteMember(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const result = await accounts.deleteCLientMember(id);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}


async function getPendingApproval(req: Request, res: Response) {
  try {

    const { id } = req.params;
    const result = await accounts.getPendingApproval(req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function approveTransactionPayment(req: Request, res: Response) {
  try {
    const result = await accounts.approveTransactionPayment(req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function rejectTransactionPayment(req: Request, res: Response) {

  try {
    const result = await accounts.rejectTransactionPayment(req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}


export default router;
