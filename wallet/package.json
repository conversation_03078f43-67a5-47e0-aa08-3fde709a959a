{"name": "wallet-service", "version": "1.2.4", "description": "", "main": "app.ts", "scripts": {"start": "NODE_OPTIONS=\"--max-old-space-size=4096\" ts-node src/app.ts", "dev": "NODE_OPTIONS=\"--max-old-space-size=4096\" nodemon --exec ts-node src/app.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:integration": "jest --testNamePattern=\"integration\"", "test:tembo": "jest tembo.integration.test.ts", "test:tembo:verbose": "jest tembo.integration.test.ts --verbose", "test:unit": "jest --testPathIgnorePatterns=\"integration\"", "test:ci": "jest --ci --coverage --watchAll=false"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"3": "^2.1.0", "@prisma/client": "^6.11.1", "aws-sdk": "^2.1536.0", "axios": "^1.6.3", "bcryptjs": "^3.0.0", "body-parser": "^1.20.2", "cors": "^2.8.5", "crypto-js": "^4.2.0", "decimal.js": "^10.6.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-fileupload": "^1.4.3", "express-ws": "^5.0.2", "firebase-admin": "^12.5.0", "googleapis": "^144.0.0", "jsonwebtoken": "^9.0.2", "knex": "^3.1.0", "mysql2": "^2.2.5", "node-cron": "^3.0.3", "node-rsa": "^1.1.1", "nodemailer": "^6.9.15", "prisma": "^6.11.1", "qrcode": "^1.5.4", "sequelize": "^6.35.2", "speakeasy": "^2.0.0", "stellar-sdk": "^7.0.0", "uuid": "^11.1.0", "xml2json": "^0.12.0", "zod": "^3.24.2"}, "devDependencies": {"@types/body-parser": "^1.19.5", "@types/cors": "^2.8.17", "@types/crypto-js": "^4.2.2", "@types/express": "^4.17.21", "@types/express-fileupload": "^1.4.4", "@types/express-ws": "^3.0.5", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20.19.9", "@types/node-cron": "^3.0.11", "@types/node-rsa": "^1.1.4", "@types/nodemailer": "^6.4.14", "@types/qrcode": "^1.5.5", "@types/request": "^2.48.12", "@types/sequelize": "^4.28.19", "@types/speakeasy": "^2.0.10", "@types/supertest": "^6.0.3", "@types/uuid": "^9.0.7", "@types/xml2json": "^0.11.6", "i": "^0.3.7", "jest": "^30.0.5", "nodemon": "^3.0.2", "npm": "^10.4.0", "redis": "^4.7.1", "supertest": "^7.1.4", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}