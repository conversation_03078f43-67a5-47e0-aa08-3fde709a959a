# Tembo Integration Test Environment Configuration
# 
# This file contains the environment variables needed for running
# comprehensive integration tests with the Tembo API.
# 
# IMPORTANT: These should be SANDBOX/TEST credentials only!
# Never use production credentials in test files.

# =============================================================================
# TEMBO API CONFIGURATION (SANDBOX)
# =============================================================================

# Tembo API Base URL (Sandbox)
TEMBO_API_URL=https://api.temboplus.com

# Tembo Authentication Token (Get from Tembo sandbox dashboard)
TEMBO_AUTH_TOKEN=your_sandbox_auth_token_here

# Tembo Account ID (Your sandbox account identifier)
TEMBO_ACCOUNT_ID=f2c84137625def12f7e0f31c

# Tembo Secret Key (For API authentication and webhook verification)
TEMBO_SECRET_KEY="E6UGukg/JaHUYlQ8Do0Tvu7Izrzu4rjZcoPpZk/scSM="

# Tembo Webhook Secret (For webhook signature verification)
TEMBO_WEBHOOK_SECRET="E6UGukg/JaHUYlQ8Do0Tvu7Izrzu4rjZcoPpZk/scSM="

# Tembo Main Account Number (For payouts and bank transfers)
TEMBO_MAIN_ACCOUNT_NO=**********

# All operations use main account - no virtual accounts needed

# =============================================================================
# WEBHOOK CONFIGURATION
# =============================================================================

# Base URL for webhook callbacks (will be set dynamically in tests)
WEBHOOK_BASE_URL=http://localhost:3000

# =============================================================================
# TEST-SPECIFIC CONFIGURATION - REAL TANZANIA CREDENTIALS
# =============================================================================

# REAL Tanzania phone number for testing (replace with your actual number)
# This will be used for both collections and mobile money payouts
TEST_PHONE_TZ=************
# Format: 255XXXXXXXXX (Tanzania country code + 9 digits)
# Network will be auto-detected (Vodacom, Tigo, Airtel)

# REAL Tanzania bank account for testing (replace with your actual details)
TEST_BANK_CODE=CRDBTZTZ
TEST_BANK_ACCOUNT=****************
TEST_BANK_ACCOUNT_NAME=Test Account Holder
# Use actual Tanzania bank details for bank transfer testing

# Test amounts (Small amounts for real testing)
TEST_AMOUNT_SMALL=1000
TEST_AMOUNT_MEDIUM=5000

# Test merchant identifiers
TEST_MERCHANT_ID=test_merchant_001
TEST_MERCHANT_NAME=Test Merchant Ltd

# =============================================================================
# DATABASE CONFIGURATION (if needed for tests)
# =============================================================================

# Test database connection (if your tests need database access)
# DB_HOST=localhost
# DB_PORT=5432
# DB_NAME=wallet_test
# DB_USER=test_user
# DB_PASSWORD=test_password

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log level for tests
LOG_LEVEL=debug

# Enable detailed API logging for debugging
ENABLE_API_LOGGING=true

# =============================================================================
# TIMEOUT CONFIGURATION
# =============================================================================

# API request timeout (milliseconds)
API_TIMEOUT=30000

# Webhook wait timeout (milliseconds)
WEBHOOK_TIMEOUT=45000

# =============================================================================
# FEATURE FLAGS FOR TESTING
# =============================================================================

# Enable/disable specific test features
ENABLE_WEBHOOK_TESTS=true
ENABLE_PAYOUT_TESTS=true
ENABLE_COLLECTION_TESTS=true
ENABLE_BALANCE_TESTS=true

# Disable Utilia integration for Tembo-only tests
DISABLE_UTILIA_INTEGRATION=true

# =============================================================================
# UTILIA CONFIGURATION (MOCKED FOR TESTS)
# =============================================================================

# Mock Utilia credentials (not used in Tembo tests)
UTILIA_SA_EMAIL=<EMAIL>
UTILIA_CONVERSION_ADDRESS=mock-conversion-address

# =============================================================================
# INSTRUCTIONS FOR SETUP
# =============================================================================

# 1. Get Tembo Sandbox Credentials:
#    - Sign up for Tembo sandbox account
#    - Get your API credentials from the dashboard
#    - Replace the placeholder values above

# 2. Configure Webhook URL:
#    - The tests will start a local webhook server
#    - Update your Tembo sandbox webhook URL to point to your test server
#    - Format: http://your-ngrok-url.ngrok.io/webhook/tembo

# 3. Real Test Credentials:
#    - Replace TEST_PHONE_TZ with your actual Tanzania phone number
#    - Replace bank details with your actual Tanzania bank account
#    - Use small amounts for testing (1000-5000 TZS)
#    - Ensure phone number is registered for mobile money

# 4. Run Tests:
#    npm test -- tembo.integration.test.ts

# =============================================================================
# SECURITY NOTES
# =============================================================================

# - This file contains SANDBOX credentials only
# - Never commit production credentials to version control
# - Use environment-specific configuration in CI/CD
# - Rotate test credentials regularly
# - Monitor test API usage to avoid rate limits
