/**
 * Environment Setup for Tests
 *
 * This file loads environment variables before tests run
 */

const path = require('path');
const result = require('dotenv').config({ path: path.resolve(__dirname, '../.env.test') });

if (result.error) {
  console.error('❌ Failed to load .env.test file:', result.error.message);
  console.error('📁 Looking for file at:', path.resolve(__dirname, '../.env.test'));
}

// Validate required test environment variables
const requiredEnvVars = [
  'TEMBO_AUTH_TOKEN',
  'TEMBO_ACCOUNT_ID',
  'TEMBO_SECRET_KEY',
  'TEMBO_MAIN_ACCOUNT_NO',
  'TEMBO_WEBHOOK_SECRET'
];

const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

if (missingVars.length > 0) {
  console.error('❌ Missing required test environment variables:');
  missingVars.forEach(varName => {
    console.error(`  - ${varName}`);
  });
  console.error('\nPlease configure .env.test file with valid Tembo sandbox credentials.');
  process.exit(1);
}

console.log('✅ Test environment variables loaded successfully');
