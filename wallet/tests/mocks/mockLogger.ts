/**
 * <PERSON><PERSON> Logger for Testing
 * 
 * This mock logger can be used in tests to:
 * 1. Capture log messages for verification
 * 2. Silence console output during tests
 * 3. Track logger method calls
 */

export interface Logger {
  info(message: string, data?: any): void;
  error(message: string, data?: any): void;
  warn(message: string, data?: any): void;
}

export interface LogEntry {
  level: 'info' | 'error' | 'warn';
  message: string;
  data?: any;
  timestamp: Date;
}

export class MockLogger implements Logger {
  public logs: LogEntry[] = [];
  public silent: boolean;

  constructor(silent: boolean = true) {
    this.silent = silent;
  }

  info(message: string, data?: any): void {
    const entry: LogEntry = {
      level: 'info',
      message,
      data,
      timestamp: new Date()
    };
    
    this.logs.push(entry);
    
    if (!this.silent) {
      console.log(`INFO: ${message}`, data || '');
    }
  }

  error(message: string, data?: any): void {
    const entry: LogEntry = {
      level: 'error',
      message,
      data,
      timestamp: new Date()
    };
    
    this.logs.push(entry);
    
    if (!this.silent) {
      console.error(`ERROR: ${message}`, data || '');
    }
  }

  warn(message: string, data?: any): void {
    const entry: LogEntry = {
      level: 'warn',
      message,
      data,
      timestamp: new Date()
    };
    
    this.logs.push(entry);
    
    if (!this.silent) {
      console.warn(`WARN: ${message}`, data || '');
    }
  }

  // Utility methods for testing
  clear(): void {
    this.logs = [];
  }

  getLogsByLevel(level: 'info' | 'error' | 'warn'): LogEntry[] {
    return this.logs.filter(log => log.level === level);
  }

  getLastLog(): LogEntry | undefined {
    return this.logs[this.logs.length - 1];
  }

  getLogCount(): number {
    return this.logs.length;
  }

  hasLogWithMessage(message: string): boolean {
    return this.logs.some(log => log.message.includes(message));
  }

  hasErrorLog(): boolean {
    return this.logs.some(log => log.level === 'error');
  }

  hasWarnLog(): boolean {
    return this.logs.some(log => log.level === 'warn');
  }

  getLogsAsString(): string {
    return this.logs.map(log => 
      `[${log.timestamp.toISOString()}] ${log.level.toUpperCase()}: ${log.message} ${log.data ? JSON.stringify(log.data) : ''}`
    ).join('\n');
  }

  // Mock specific methods for Jest expectations
  expectInfoLogWithMessage(message: string): void {
    const hasLog = this.logs.some(log => 
      log.level === 'info' && log.message.includes(message)
    );
    if (!hasLog) {
      throw new Error(`Expected info log with message containing "${message}", but not found. Logs: ${this.getLogsAsString()}`);
    }
  }

  expectErrorLogWithMessage(message: string): void {
    const hasLog = this.logs.some(log => 
      log.level === 'error' && log.message.includes(message)
    );
    if (!hasLog) {
      throw new Error(`Expected error log with message containing "${message}", but not found. Logs: ${this.getLogsAsString()}`);
    }
  }

  expectNoErrorLogs(): void {
    const errorLogs = this.getLogsByLevel('error');
    if (errorLogs.length > 0) {
      throw new Error(`Expected no error logs, but found ${errorLogs.length}. Error logs: ${errorLogs.map(log => log.message).join(', ')}`);
    }
  }
}

// Factory function for creating mock loggers
export function createMockLogger(options: { silent?: boolean } = {}): MockLogger {
  return new MockLogger(options.silent ?? true);
}

// Jest matcher extensions for logger testing
declare global {
  namespace jest {
    interface Matchers<R> {
      toHaveLoggedInfo(message: string): R;
      toHaveLoggedError(message: string): R;
      toHaveLoggedWarn(message: string): R;
      toHaveLogCount(count: number): R;
      toHaveNoErrorLogs(): R;
    }
  }
}

// Export for Jest setup
export const mockLoggerMatchers = {
  toHaveLoggedInfo(received: MockLogger, message: string) {
    const hasLog = received.hasLogWithMessage(message) && 
                   received.getLogsByLevel('info').some(log => log.message.includes(message));
    
    return {
      message: () => hasLog 
        ? `Expected not to have info log with message "${message}"`
        : `Expected to have info log with message "${message}". Actual logs: ${received.getLogsAsString()}`,
      pass: hasLog,
    };
  },

  toHaveLoggedError(received: MockLogger, message: string) {
    const hasLog = received.getLogsByLevel('error').some(log => log.message.includes(message));
    
    return {
      message: () => hasLog 
        ? `Expected not to have error log with message "${message}"`
        : `Expected to have error log with message "${message}". Actual logs: ${received.getLogsAsString()}`,
      pass: hasLog,
    };
  },

  toHaveLoggedWarn(received: MockLogger, message: string) {
    const hasLog = received.getLogsByLevel('warn').some(log => log.message.includes(message));
    
    return {
      message: () => hasLog 
        ? `Expected not to have warn log with message "${message}"`
        : `Expected to have warn log with message "${message}". Actual logs: ${received.getLogsAsString()}`,
      pass: hasLog,
    };
  },

  toHaveLogCount(received: MockLogger, expectedCount: number) {
    const actualCount = received.getLogCount();
    
    return {
      message: () => `Expected ${expectedCount} logs, but got ${actualCount}. Logs: ${received.getLogsAsString()}`,
      pass: actualCount === expectedCount,
    };
  },

  toHaveNoErrorLogs(received: MockLogger) {
    const errorLogs = received.getLogsByLevel('error');
    const hasNoErrors = errorLogs.length === 0;
    
    return {
      message: () => hasNoErrors 
        ? `Expected to have error logs, but found none`
        : `Expected no error logs, but found ${errorLogs.length}: ${errorLogs.map(log => log.message).join(', ')}`,
      pass: hasNoErrors,
    };
  }
};
