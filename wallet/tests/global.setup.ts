/**
 * Global Test Setup
 *
 * Runs once before all tests start
 */

import { config } from 'dotenv';
import { resolve } from 'path';

export default async function globalSetup() {
  // Load environment variables from .env.test
  const envPath = resolve(__dirname, '../.env.test');
  const result = config({ path: envPath });

  if (result.error) {
    console.error('❌ Failed to load .env.test file:', result.error.message);
    console.error('📁 Looking for file at:', envPath);
    throw new Error('Failed to load test environment configuration');
  }

  console.log('🚀 Starting Tembo integration tests...');
  console.log('📋 Test environment:', process.env.NODE_ENV || 'test');
  console.log('🌐 Tembo API URL:', process.env.TEMBO_API_URL);
  console.log('🔧 Webhook base URL:', process.env.WEBHOOK_BASE_URL);

  // Validate test environment
  if (!process.env.TEMBO_AUTH_TOKEN) {
    throw new Error('Test environment not properly configured. Check .env.test file.');
  }

  console.log('✅ Global test setup completed');
}
