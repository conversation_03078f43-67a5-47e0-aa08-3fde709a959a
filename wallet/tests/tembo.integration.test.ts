import { describe, test, expect, beforeAll, afterAll } from '@jest/globals';
import TemboService from '../src/intergrations/Tembo';
import { MockLogger } from './mocks/mockLogger';

/**
 * Focused Tembo Integration Tests - Core API Endpoints
 *
 * This test suite focuses on testing specific TemboService API endpoints:
 * 1. Balance & Account Operations
 * 2. Mobile Money Collections (C2B)
 * 3. Mobile Money Payouts (B2C)
 * 4. Bank Transfers
 * 5. Virtual Account Management
 * 6. Transaction Status Checking
 * 7. Webhook Processing
 *
 * Prerequisites:
 * - .env.test file with valid Tembo sandbox credentials
 * - Real Tanzania phone number for testing
 * - Small test amounts (1000-5000 TZS)
 */

describe('Tembo API Integration Tests - Core Endpoints', () => {
  let temboService: TemboService;
  let mockLogger: MockLogger;
  
  // Test configuration
  const testConfig = {
    testPhone: process.env.TEST_PHONE_TZ || '************',
    testAmount: '100000', // 100000 TZS for testing (larger sum for multiple payouts)
    testMerchantId: 'test_merchant_api',
    testBankCode: 'BARCTZTZ',
    testBankAccount: '**********',
    testAccountName: 'Muda Ventures Limited'
  };

  // Store transaction references for status checking
  const transactionRefs: string[] = [];

  beforeAll(async () => {
    // Load test environment
    require('dotenv').config({ path: '.env.test' });

    // Verify required environment variables
    const requiredVars = ['TEMBO_AUTH_TOKEN', 'TEMBO_ACCOUNT_ID', 'TEMBO_SECRET_KEY', 'TEMBO_MAIN_ACCOUNT_NO'];
    for (const envVar of requiredVars) {
      if (!process.env[envVar]) {
        throw new Error(`Missing required environment variable: ${envVar}`);
      }
    }

    // Initialize mock logger (non-silent to see API logs)
    mockLogger = new MockLogger(false);

    // Initialize TemboService with mock logger
    temboService = new TemboService(mockLogger);

    console.log('🚀 Starting Tembo API endpoint tests');
    console.log(`📱 Test phone: ${testConfig.testPhone}`);
    console.log(`💰 Test amount: ${testConfig.testAmount} TZS`);
  });

  afterAll(() => {
    console.log('✅ Tembo API tests completed');
    console.log(`📊 Tested ${transactionRefs.length} transactions`);
    console.log(`📝 Logger captured ${mockLogger.getLogCount()} log entries`);
  });

  // ==================== BALANCE & ACCOUNT OPERATIONS ====================

  describe('Balance & Account Operations', () => {
    test('should have correct main account number configured', () => {
      // Verify the environment variable is set correctly
      expect(process.env.TEMBO_MAIN_ACCOUNT_NO).toBe('**********');

      console.log('✅ Main Account Number configured:', process.env.TEMBO_MAIN_ACCOUNT_NO);
    });

    test('GET /tembo/v1/wallet/main-balance - Get main account balance', async () => {
      console.log('🏦 Testing main balance endpoint...');
      
      const result = await temboService.getMainBalance();

      expect(result).toBeDefined();
      expect(result.status).toBe(200);
      expect(result.data).toBeDefined();
      
      // Log balance information
      console.log('✅ Main Balance Response:', {
        status: result.status,
        hasBalance: !!result.data?.availableBalance,
        data: result.data
      });
    }, 15000);

    test('GET /tembo/v1/wallet/collection-balance - Get collection balance', async () => {
      console.log('💰 Testing collection balance endpoint...');
      
      const result = await temboService.getCollectionBalance();

      expect(result).toBeDefined();
      expect(result.status).toBe(200);
      expect(result.data).toBeDefined();
      
      console.log('✅ Collection Balance Response:', {
        status: result.status,
        hasBalance: !!result.data?.availableBalance,
        data: result.data
      });
    }, 15000);

    test('POST /tembo/v1/wallet/collection-statement - Get collection statement', async () => {
      console.log('📄 Testing collection statement endpoint...');
      
      const endDate = new Date().toISOString().split('T')[0];
      const startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
      
      const result = await temboService.getCollectionStatement(startDate, endDate);

      expect(result).toBeDefined();
      expect(result.status).toBe(200);
      
      console.log('✅ Collection Statement Response:', {
        status: result.status,
        dateRange: `${startDate} to ${endDate}`,
        hasStatement: !!result.data,
        transactionCount: result.data?.statement?.length || 0
      });
    }, 15000);
  });


  // ==================== MOBILE MONEY COLLECTIONS (C2B) ====================

  // describe('Mobile Money Collections (C2B)', () => {
  //   test('POST /tembo/v1/collection - M-Pesa collection request', async () => {
  //     const transactionId = `TEST_MPESA_C2B_${Date.now()}`;
  //     transactionRefs.push(transactionId);

  //     console.log(`📱 Testing M-Pesa collection: ${transactionId}`);
  //     console.log(`📞 Phone: ${testConfig.testPhone}`);
  //     console.log(`💰 Amount: ${testConfig.testAmount} TZS (Large sum for multiple payouts)`);
      
  //     const result = await temboService.makeMMPullRequest(
  //       testConfig.testMerchantId,
  //       transactionId,
  //       testConfig.testAmount,
  //       testConfig.testPhone
  //     );

  //     expect(result).toBeDefined();
  //     expect(result.status).toBe(200);
  //     expect(result.trans_id).toBe(transactionId);
      
  //     console.log('✅ M-Pesa Collection Initiated:', {
  //       status: result.status,
  //       transactionId: result.trans_id,
  //       amount: result.amount_transfered,
  //       temboRef: result.data?.transactionRef,
  //       statusCode: result.data?.statusCode
  //     });
  //   }, 20000);
  // });

  // ==================== MOBILE MONEY PAYOUTS (B2C) ====================

  describe('Mobile Money Payouts (B2C) - Multiple M-Pesa Payouts', () => {
    test('POST /tembo/v1/payment/wallet-to-mobile - Bank transfer payout #1 (2000 TZS)', async () => {
      const transactionId = `TEST_BANK_B2C_1_${Date.now()}`;
      const payoutAmount = '2000'; // 40,000 TZS
      transactionRefs.push(transactionId);

      console.log(`🏦 Testing Bank transfer payout #1: ${transactionId}`);
      console.log(`🏛️ Bank: ${testConfig.testBankCode}`);
      console.log(`💰 Amount: ${payoutAmount} TZS`);
      
      const result = await temboService.payToBank(
        testConfig.testMerchantId,
        transactionId,
        payoutAmount,
        testConfig.testBankCode,
        testConfig.testBankAccount,
        testConfig.testAccountName
      );

      expect(result).toBeDefined();
      expect(result.status).toBe(200);
      
      console.log('✅ Bank Transfer Payout #1 Initiated:', {
        status: result.status,
        transactionId: result.trans_id,
        amount: result.amount_transfered,
        bankCode: testConfig.testBankCode,
        statusCode: result.data?.statusCode
      });
    }, 25000);

    test('POST /tembo/v1/payment/wallet-to-mobile - M-Pesa payout #2 (5000 TZS)', async () => {
      const transactionId = `TEST_MPESA_B2C_2_${Date.now()}`;
      const payoutAmount = '5000'; // 12,000 TZS
      transactionRefs.push(transactionId);

      console.log(`📤 Testing M-Pesa payout #2: ${transactionId}`);
      console.log(`📞 Phone: ${testConfig.testPhone}`);
      console.log(`💰 Amount: ${payoutAmount} TZS`);
      
      const result = await temboService.makeMMPushRequest(
        testConfig.testMerchantId,
        transactionId,
        payoutAmount,
        testConfig.testPhone
      );

      expect(result).toBeDefined();
      expect(result.status).toBe(200);
      
      console.log('✅ M-Pesa Payout #2 Initiated:', {
        status: result.status,
        transactionId: result.trans_id,
        amount: result.amount_transfered,
        statusCode: result.data?.statusCode
      });
    }, 25000);

    test('POST /tembo/v1/payment/wallet-to-mobile - M-Pesa payout #3 (2000 TZS)', async () => {
      const transactionId = `TEST_MPESA_B2C_3_${Date.now()}`;
      const payoutAmount = '2000'; // 8,000 TZS
      transactionRefs.push(transactionId);

      console.log(`📤 Testing M-Pesa payout #3: ${transactionId}`);
      console.log(`📞 Phone: ${testConfig.testPhone}`);
      console.log(`💰 Amount: ${payoutAmount} TZS`);
      
      const result = await temboService.makeMMPushRequest(
        testConfig.testMerchantId,
        transactionId,
        payoutAmount,
        testConfig.testPhone
      );

      expect(result).toBeDefined();
      expect(result.status).toBe(200);
      
      console.log('✅ M-Pesa Payout #3 Initiated:', {
        status: result.status,
        transactionId: result.trans_id,
        amount: result.amount_transfered,
        statusCode: result.data?.statusCode
      });
    }, 25000);

    test('POST /tembo/v1/payment/wallet-to-mobile - M-Pesa payout #4 (2000 TZS)', async () => {
      const transactionId = `TEST_MPESA_B2C_4_${Date.now()}`;
      const payoutAmount = '2000'; // 10,000 TZS
      transactionRefs.push(transactionId);

      console.log(`📤 Testing M-Pesa payout #4: ${transactionId}`);
      console.log(`📞 Phone: ${testConfig.testPhone}`);
      console.log(`💰 Amount: ${payoutAmount} TZS`);
      
      const result = await temboService.makeMMPushRequest(
        testConfig.testMerchantId,
        transactionId,
        payoutAmount,
        testConfig.testPhone
      );

      expect(result).toBeDefined();
      expect(result.status).toBe(200);
      
      console.log('✅ M-Pesa Payout #4 Initiated:', {
        status: result.status,
        transactionId: result.trans_id,
        amount: result.amount_transfered,
        statusCode: result.data?.statusCode
      });
    }, 25000);

    test('POST /tembo/v1/payment/wallet-to-mobile - Bank transfer payout #5 (2000 TZS)', async () => {
      const transactionId = `TEST_BANK_B2C_5_${Date.now()}`;
      const payoutAmount = '2000'; // 30,000 TZS
      const nmbBankCode = testConfig.testBankCode; // Use NMB for variety
      transactionRefs.push(transactionId);

      console.log(`🏦 Testing Bank transfer payout #5: ${transactionId}`);
      console.log(`🏛️ Bank: ${nmbBankCode}`);
      console.log(`💰 Amount: ${payoutAmount} TZS`);
      
      const result = await temboService.payToBank(
        testConfig.testMerchantId,
        transactionId,
        payoutAmount,
        nmbBankCode,
        testConfig.testBankAccount,
        testConfig.testAccountName
      );

      expect(result).toBeDefined();
      expect(result.status).toBe(200);
      
      console.log('✅ Bank Transfer Payout #5 Initiated:', {
        status: result.status,
        transactionId: result.trans_id,
        amount: result.amount_transfered,
        bankCode: nmbBankCode,
        statusCode: result.data?.statusCode
      });
      
      // Log summary of all payouts
      console.log('\n📊 Payout Summary:');
      console.log('💸 Total payouts: 5 transactions');
      console.log('🏦 Bank transfers: 2 (40,000 + 30,000 = 70,000 TZS to BARCTZTZ)');
      console.log('📱 Mobile money: 3 (12,000 + 8,000 + 10,000 = 30,000 TZS to M-Pesa)');
      console.log('💰 Total amount: 100,000 TZS (40k + 12k + 8k + 10k + 30k)');
      console.log('🎯 All from original collection of 100,000 TZS');
      console.log('✅ Perfect balance: Collection = Payouts');
    }, 25000);
  });

  //   test('POST /tembo/v1/payment/wallet-to-mobile - Tigo Pesa payout', async () => {
  //     const transactionId = `TEST_TIGO_B2C_${Date.now()}`;
  //     transactionRefs.push(transactionId);
      
  //     const tigoPhone = '************';

  //     console.log(`📤 Testing Tigo Pesa payout: ${transactionId}`);
      
  //     const result = await temboService.makeMMPushRequest(
  //       testConfig.testMerchantId,
  //       transactionId,
  //       testConfig.testAmount,
  //       tigoPhone
  //     );

  //     expect(result).toBeDefined();
  //     expect(result.status).toBe(200);
      
  //     console.log('✅ Tigo Pesa Payout Initiated:', {
  //       status: result.status,
  //       transactionId: result.trans_id,
  //       network: 'TIGO'
  //     });
  //   }, 20000);

  //   test('POST /tembo/v1/payment/wallet-to-mobile - Airtel Money payout', async () => {
  //     const transactionId = `TEST_AIRTEL_B2C_${Date.now()}`;
  //     transactionRefs.push(transactionId);
      
  //     const airtelPhone = '************';

  //     console.log(`📤 Testing Airtel Money payout: ${transactionId}`);
      
  //     const result = await temboService.makeMMPushRequest(
  //       testConfig.testMerchantId,
  //       transactionId,
  //       testConfig.testAmount,
  //       airtelPhone
  //     );

  //     expect(result).toBeDefined();
  //     expect(result.status).toBe(200);
      
  //     console.log('✅ Airtel Money Payout Initiated:', {
  //       status: result.status,
  //       transactionId: result.trans_id,
  //       network: 'AIRTEL'
  //     });
  //   }, 20000);
  // });

  // // ==================== BANK TRANSFERS ====================

  // describe('Bank Transfers', () => {
  //   test('POST /tembo/v1/payment/wallet-to-mobile - Bank transfer to CRDB', async () => {
  //     const transactionId = `TEST_BANK_CRDB_${Date.now()}`;
  //     transactionRefs.push(transactionId);

  //     console.log(`🏦 Testing bank transfer: ${transactionId}`);
  //     console.log(`🏛️ Bank: ${testConfig.testBankCode}`);
  //     console.log(`💰 Amount: ${testConfig.testAmount} TZS`);
      
  //     const result = await temboService.payToBank(
  //       testConfig.testMerchantId,
  //       transactionId,
  //       testConfig.testAmount,
  //       testConfig.testBankCode,
  //       testConfig.testBankAccount,
  //       testConfig.testAccountName
  //     );

  //     expect(result).toBeDefined();
  //     expect(result.status).toBe(200);
      
  //     console.log('✅ Bank Transfer Initiated:', {
  //       status: result.status,
  //       transactionId: result.trans_id,
  //       bankCode: testConfig.testBankCode,
  //       amount: result.amount_transfered,
  //       statusCode: result.data?.statusCode
  //     });
  //   }, 20000);

  //   test('POST /tembo/v1/payment/wallet-to-mobile - Bank transfer to NMB', async () => {
  //     const transactionId = `TEST_BANK_NMB_${Date.now()}`;
  //     transactionRefs.push(transactionId);
      
  //     const nmbBankCode = 'NMIBTZTZ';

  //     console.log(`🏦 Testing NMB bank transfer: ${transactionId}`);
      
  //     const result = await temboService.payToBank(
  //       testConfig.testMerchantId,
  //       transactionId,
  //       testConfig.testAmount,
  //       nmbBankCode,
  //       testConfig.testBankAccount,
  //       testConfig.testAccountName
  //     );

  //     expect(result).toBeDefined();
  //     expect(result.status).toBe(200);
      
  //     console.log('✅ NMB Bank Transfer Initiated:', {
  //       status: result.status,
  //       transactionId: result.trans_id,
  //       bankCode: nmbBankCode
  //     });
  //   }, 20000);
  // });



  // ==================== UTILITY METHODS ====================

  // describe('Utility Methods', () => {
  //   test('Phone number validation - Valid Tanzania numbers', () => {
  //     console.log('📱 Testing phone number validation...');
      
  //     const validNumbers = [
  //       '************', // Vodacom M-Pesa
  //       '************', // Tigo Pesa
  //       '************'  // Airtel Money
  //     ];

  //     for (const phone of validNumbers) {
  //       const result = temboService.validatePhoneNumber(phone);
  //       expect(result.valid).toBe(true);
        
  //       console.log(`✅ ${phone}: Valid (${result.valid ? 'PASS' : 'FAIL'})`);
  //     }
  //   });

  //   test('Phone number validation - Invalid numbers', () => {
  //     const invalidNumbers = [
  //       '*********',     // Too short
  //       '************',  // Wrong prefix
  //       '************'   // Kenya code
  //     ];

  //     for (const phone of invalidNumbers) {
  //       const result = temboService.validatePhoneNumber(phone);
  //       expect(result.valid).toBe(false);
  //       expect(result.message).toBeDefined();
        
  //       console.log(`❌ ${phone}: Invalid - ${result.message}`);
  //     }
  //   });

  //   test('Supported networks and currencies', () => {
  //     console.log('🌐 Testing utility methods...');
      
  //     const networks = temboService.getSupportedNetworks();
  //     const currencies = temboService.getSupportedCurrencies();
      
  //     expect(networks).toContain('TZ-VODACOM-C2B');
  //     expect(networks).toContain('TZ-TIGO-B2C');
  //     expect(networks).toContain('TZ-AIRTEL-C2B');
  //     expect(currencies).toContain('TZS');
      
  //     console.log('✅ Supported Networks:', networks);
  //     console.log('✅ Supported Currencies:', currencies);
  //   });
  // });
});