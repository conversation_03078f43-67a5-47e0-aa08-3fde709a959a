/**
 * Jest Test Setup
 *
 * This file runs before each test file and sets up the testing environment
 */

import { config } from 'dotenv';
import { expect } from '@jest/globals';
import { resolve } from 'path';
import { mockLoggerMatchers } from './mocks/mockLogger';

// Load test environment variables
const envPath = resolve(__dirname, '../.env.test');
const result = config({ path: envPath });

if (result.error) {
  console.error('❌ Failed to load .env.test file:', result.error.message);
  console.error('📁 Looking for file at:', envPath);
}

// Extend Jest matchers if needed
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeValidTemboResponse(): R;
    }
  }
}

expect.extend({
  toBeValidTemboResponse(received: any) {
    const pass = received &&
                 typeof received.status === 'number' &&
                 received.status >= 200 &&
                 received.status < 300;

    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid Tembo response`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid Tembo response`,
        pass: false,
      };
    }
  },
  // Add mock logger matchers
  ...mockLoggerMatchers
});

// Global test configuration
declare global {
  var testConfig: {
    timeout: {
      short: number;
      medium: number;
      long: number;
      webhook: number;
    };
    retries: {
      api: number;
      webhook: number;
    };
  };
  var cleanup: () => void;
}

global.testConfig = {
  timeout: {
    short: 5000,
    medium: 15000,
    long: 30000,
    webhook: 45000
  },
  retries: {
    api: 3,
    webhook: 5
  }
};

// Console logging for tests
const originalConsoleLog = console.log;
console.log = (...args: any[]) => {
  const timestamp = new Date().toISOString();
  originalConsoleLog(`[${timestamp}]`, ...args);
};

// Global error handler for unhandled promises
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Cleanup function
global.cleanup = () => {
  // Add any global cleanup logic here
  console.log('🧹 Running global test cleanup');
};
