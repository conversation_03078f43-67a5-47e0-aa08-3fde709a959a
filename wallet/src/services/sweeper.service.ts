import Model from "../helpers/model";
import {
  listWalletsForVault,
  getUserWallet,
  queryBalances,
  getWalletBalances,
  initiatePayout,
  batchGetAssets,
  initiateSponsoredTransfer
} from "../intergrations/Utilia";
import { getItem, setItem } from "../helpers/connectRedis";

export interface SweepResult {
  walletId: string;
  walletName: string;
  asset: string;
  amount: string;
  status: 'success' | 'failed' | 'skipped';
  error?: string;
  transactionId?: string;
}

export interface SweepOptions {
  minAmount?: string; // Minimum amount to sweep (default: 0)
  excludeWallets?: string[]; // Wallet IDs to exclude from sweeping
  includeAssets?: string[]; // Only sweep specific assets
  excludeAssets?: string[]; // Assets to exclude from sweeping
  dryRun?: boolean; // If true, don't actually execute transfers
  memo?: string; // Memo for the transfer
  gasThreshold?: string; // Minimum gas balance required
}

export interface SweepFromWalletOptions {
  walletId: string;
  destinationWalletId: string;
  minAmount?: string;
  includeAssets?: string[];
  excludeAssets?: string[];
  dryRun?: boolean;
  memo?: string;
}

export class SweeperService extends Model {
  /**
   * Sweeper Service - Automated Fund Management System
   * 
   * PROCESS FLOW:
   * 1. GAS WALLET MANAGEMENT:
   *    - Gas wallet (cea08904a71d) contains gas fees for all operations
   *    - Before sweeping from any wallet, check if it has sufficient gas balance
   *    - If gas balance is insufficient, transfer gas from gas wallet to target wallet
   * 
   * 2. WALLET CLASSIFICATION:
   *    - TARGET WALLETS: All wallets starting with "10" (e.g., 10339707, 10819033)
   *    - EXEMPTED WALLETS: All wallets NOT starting with "10" (system wallets)
   *    - COLD WALLET: Main destination (9a185f915491) - receives all swept funds
   *    - GAS WALLET: Gas fee provider (cea08904a71d) - provides gas for operations
   * 
   * 3. SWEEPING PROCESS:
   *    - Scan all wallets in the vault
   *    - Filter to only wallets starting with "10"
   *    - For each target wallet:
   *      a. Check gas balance for the wallet's network
   *      b. If gas insufficient, transfer gas from gas wallet
   *      c. Sweep all assets above minimum threshold to cold wallet
   *      d. Log all operations for audit trail
   * 
   * 4. SAFETY FEATURES:
   *    - Dry run mode for testing
   *    - Minimum amount thresholds
   *    - Asset inclusion/exclusion lists
   *    - Concurrent operation prevention
   *    - Comprehensive error handling and logging
   */
  private readonly COLD_WALLET_ID: string;
  private readonly GAS_WALLET_ID: string;
  private readonly MIN_SWEEP_AMOUNT: string;
  private readonly SWEEP_CACHE_KEY: string = 'sweeper:last_run';
  private readonly SWEEP_LOCK_KEY: string = 'sweeper:lock';

  constructor() {
    super();
    this.COLD_WALLET_ID = process.env.COLD_WALLET_ID || '9a185f915491';
    this.GAS_WALLET_ID = process.env.GAS_WALLET_ID || 'cea08904a71d';
    this.MIN_SWEEP_AMOUNT = process.env.MIN_SWEEP_AMOUNT || '2';
  }

  /**
   * Check and transfer gas if needed for a target wallet
   */
  private async checkAndTransferGas(targetWalletId: string, targetWalletName: string, dryRun: boolean = false): Promise<{
    transferred: boolean;
    amount?: string;
    asset?: string;
    error?: string;
  }> {
    try {
      // Get target wallet balances to check gas
      const targetBalancesResponse = await getWalletBalances({ walletId: targetWalletId });
      if (targetBalancesResponse.error) {
        return { transferred: false, error: `Failed to get target wallet balances: ${targetBalancesResponse.error.message}` };
      }

      const targetBalances = targetBalancesResponse.data.walletBalances || [];

      // Get gas wallet balances
      const gasBalancesResponse = await getWalletBalances({ walletId: this.GAS_WALLET_ID });
      if (gasBalancesResponse.error) {
        return { transferred: false, error: `Failed to get gas wallet balances: ${gasBalancesResponse.error.message}` };
      }

      const gasBalances = gasBalancesResponse.data.walletBalances || [];

      // Check for native gas tokens (ETH, BNB, etc.) in target wallet
      const targetGasTokens = targetBalances.filter((balance: any) => {
        const asset = balance.asset;
        return asset.includes('native.') && parseFloat(balance.value) > 0;
      });

      // Check for native gas tokens in gas wallet
      const gasWalletTokens = gasBalances.filter((balance: any) => {
        const asset = balance.asset;
        return asset.includes('native.') && parseFloat(balance.value) > 0;
      });

      // If target wallet has no gas tokens but gas wallet does, transfer gas
      if (targetGasTokens.length === 0 && gasWalletTokens.length > 0) {
        const gasToken = gasWalletTokens[0]; // Use first available gas token
        const gasAmount = '0.01'; // Transfer a small amount for gas fees

        console.log(`⛽ Target wallet ${targetWalletName} needs gas. Transferring ${gasAmount} ${gasToken.asset} from gas wallet`);

        if (!dryRun) {
          const gasTransferResult = await this.executeSweep({
            sourceWalletId: this.GAS_WALLET_ID,
            destinationWalletId: targetWalletId,
            asset: gasToken.asset,
            amount: gasAmount,
            memo: `Gas transfer for sweep operation`
          });

          if (gasTransferResult.success) {
            return {
              transferred: true,
              amount: gasAmount,
              asset: gasToken.asset
            };
          } else {
            return { transferred: false, error: `Gas transfer failed: ${gasTransferResult.error}` };
          }
        } else {
          // Dry run - just return that we would transfer
          return {
            transferred: true,
            amount: gasAmount,
            asset: gasToken.asset
          };
        }
      }

      return { transferred: false };

    } catch (error: any) {
      return { transferred: false, error: `Gas check failed: ${error.message}` };
    }
  }

  private async getSupportedAssets(includeAssetCodes?: string[]): Promise<any[]> {
    try {
      let query = `
        SELECT asset, asset_code, network, chain
        FROM utilia_assets 
        WHERE is_muda_supported = 1
      `;

      if (includeAssetCodes && includeAssetCodes.length > 0) {
        const assetCodes = includeAssetCodes.map(code => `'${code}'`).join(',');
        query += ` AND asset_code IN (${assetCodes})`;
      }

      query += ` ORDER BY asset_code, network`;

      const result = await this.callQuery(query);
      return Array.isArray(result) ? result : [];
    } catch (error: any) {
      console.error('Failed to get supported assets:', error);
      return [];
    }
  }

  /**
   * Check if a wallet is eligible for sweeping
   */
  private async isWalletEligibleForSweep(walletId: string, walletName: string, options: SweepOptions = {}): Promise<{
    eligible: boolean;
    reason?: string;
    assets?: any[];
    totalEligibleAmount?: string;
  }> {
    try {
      // Skip if this is the cold storage wallet itself
      if (walletId === this.COLD_WALLET_ID) {
        return { eligible: false, reason: 'Cold storage wallet - skipping' };
      }

      // Skip if this is the gas wallet
      if (walletId === this.GAS_WALLET_ID) {
        return { eligible: false, reason: 'Gas wallet - skipping' };
      }

      // Get wallet balances
      const balanceResponse = await getWalletBalances({ walletId });
      console.log('balanceResponse', JSON.stringify(balanceResponse, null, 2));
      if (balanceResponse.error) {
        return { eligible: false, reason: `Failed to get wallet balance: ${balanceResponse.error.message}` };
      }

      const balances = balanceResponse.data.walletBalances || [];

      if (balances.length === 0) {
        return { eligible: false, reason: 'No balances found' };
      }

      // Get supported assets from database
      const supportedAssets = await this.getSupportedAssets(options.includeAssets);
      const supportedAssetIds = supportedAssets.map((asset: any) => asset.asset);

      console.log(`📋 Found ${supportedAssets.length} supported assets for sweeping`);
      supportedAssets.forEach((asset: any, index: number) => {
        console.log(`   ${index + 1}. ${asset.asset_code} (${asset.network}/${asset.chain})`);
        console.log(`      Asset ID: ${asset.asset}`);
      });

      // Check for eligible assets
      const eligibleAssets = balances.filter((balance: any) => {
        const asset = balance.asset;
        const amount = balance.value || '0';

        // Skip if amount is below minimum
        if (parseFloat(amount) < parseFloat(options.minAmount || this.MIN_SWEEP_AMOUNT)) {
          return false;
        }

        // Skip if asset is excluded
        if (options.excludeAssets?.includes(asset)) {
          return false;
        }

        // Skip if specific assets are required and this one isn't included
        if (supportedAssetIds.length > 0 && !supportedAssetIds.includes(asset)) {
          return false;
        }

        return true;
      });

      if (eligibleAssets.length === 0) {
        return { eligible: false, reason: 'No eligible assets found (below minimum amount or excluded)' };
      }

      // Calculate total eligible amount
      const totalEligibleAmount = eligibleAssets.reduce((total: number, asset: any) => {
        return total + parseFloat(asset.value || '0');
      }, 0).toString();

      return {
        eligible: true,
        assets: eligibleAssets,
        totalEligibleAmount
      };

    } catch (error: any) {
      return { eligible: false, reason: `Error checking eligibility: ${error.message}` };
    }
  }

  /**
   * Sweep funds from all wallets to cold storage
   */
  async sweepFromAllWallets(options: SweepOptions = {}): Promise<{
    success: boolean;
    message: string;
    results: SweepResult[];
    summary: {
      totalWallets: number;
      successfulSweeps: number;
      failedSweeps: number;
      skippedSweeps: number;
      totalAmount: string;
    };
  }> {
    try {
      // Check if sweep is already running
      const isLocked = await this.checkSweepLock();
      if (isLocked) {
        return {
          success: false,
          message: 'Sweep operation already in progress',
          results: [],
          summary: {
            totalWallets: 0,
            successfulSweeps: 0,
            failedSweeps: 0,
            skippedSweeps: 0,
            totalAmount: '0'
          }
        };
      }

      // Set sweep lock
      await this.setSweepLock();

      const results: SweepResult[] = [];
      let totalAmount = '0';

      // Get all wallets in the vault
      const walletsResponse = await listWalletsForVault();
      if (walletsResponse.error) {
        throw new Error(`Failed to fetch wallets: ${walletsResponse.error.message}`);
      }

      const wallets = walletsResponse.data.wallets || [];
      const filteredWallets = wallets.filter((wallet: any) => {
        const walletId = wallet.name.split('/').pop();
        const walletName = wallet.displayName || walletId;

        // Only include wallets that start with "10" in their name
        const startsWith10 = walletName.startsWith('10');

        // Also check if wallet is not in exclude list
        const notExcluded = !options.excludeWallets?.includes(walletId);

        return startsWith10 && notExcluded;
      });

      console.log(`Found ${filteredWallets.length} wallets to process (filtered to only wallets starting with '10')`);

      // Process each wallet using sweepFromWallet
      for (const wallet of filteredWallets) {
        const walletId = wallet.name.split('/').pop();
        const walletName = wallet.displayName || walletId;

        console.log(`Processing wallet: ${walletName} (${walletId})`);

        try {
          // Call sweepFromWallet for each wallet
          const walletResult = await this.sweepFromWallet({
            walletId,
            destinationWalletId: this.COLD_WALLET_ID,
            minAmount: options.minAmount,
            includeAssets: options.includeAssets,
            excludeAssets: options.excludeAssets,
            dryRun: options.dryRun,
            memo: options.memo || `Sweep from ${walletName}`
          });

          // Add results from this wallet to overall results
          results.push(...walletResult.results);

          // Update total amount
          if (walletResult.summary.totalAmount) {
            totalAmount = (parseFloat(totalAmount) + parseFloat(walletResult.summary.totalAmount)).toString();
          }

        } catch (error: any) {
          console.error(`Failed to sweep from wallet ${walletName}:`, error.message);
          results.push({
            walletId,
            walletName,
            asset: 'all',
            amount: '0',
            status: 'failed',
            error: error.message
          });
        }
      }

      // Calculate summary
      const summary = {
        totalWallets: filteredWallets.length,
        successfulSweeps: results.filter(r => r.status === 'success').length,
        failedSweeps: results.filter(r => r.status === 'failed').length,
        skippedSweeps: results.filter(r => r.status === 'skipped').length,
        totalAmount
      };

      // Update last run timestamp
      await this.updateLastSweepRun();

      // Release sweep lock
      await this.releaseSweepLock();

      return {
        success: true,
        message: `Sweep completed. ${summary.successfulSweeps} successful, ${summary.failedSweeps} failed, ${summary.skippedSweeps} skipped`,
        results,
        summary
      };

    } catch (error: any) {
      await this.releaseSweepLock();
      console.error('Sweep from all wallets failed:', error);
      return {
        success: false,
        message: `Sweep failed: ${error.message}`,
        results: [],
        summary: {
          totalWallets: 0,
          successfulSweeps: 0,
          failedSweeps: 0,
          skippedSweeps: 0,
          totalAmount: '0'
        }
      };
    }
  }

  /**
   * Sweep funds from a specific wallet to a destination address
   */
  async sweepFromWallet(options: SweepFromWalletOptions): Promise<{
    success: boolean;
    message: string;
    results: SweepResult[];
    summary: {
      totalAssets: number;
      successfulSweeps: number;
      failedSweeps: number;
      skippedSweeps: number;
      totalAmount: string;
    };
  }> {
    try {
      const { walletId, destinationWalletId, minAmount, includeAssets, excludeAssets, dryRun, memo } = options;

      if (!destinationWalletId) {
        throw new Error('Destination wallet ID is required');
      }

      const results: SweepResult[] = [];
      let totalAmount = '0';

      // Get wallet information
      const walletResponse = await getUserWallet({ walletId });
      if (walletResponse.error) {
        throw new Error(`Failed to get wallet: ${walletResponse.error.message}`);
      }

      const walletData = walletResponse.data.wallet;
      const walletName = walletData.displayName || walletId;
      const balances = walletData.balances || [];

      console.log(`Sweeping from wallet: ${walletName} (${walletId})`);

      // Check if wallet is eligible for sweeping
      const eligibility = await this.isWalletEligibleForSweep(walletId, walletName, {
        minAmount: options.minAmount,
        includeAssets: options.includeAssets,
        excludeAssets: options.excludeAssets
      });

      if (!eligibility.eligible) {
        return {
          success: false,
          message: `Wallet not eligible: ${eligibility.reason}`,
          results: [{
            walletId,
            walletName,
            asset: 'all',
            amount: '0',
            status: 'skipped',
            error: eligibility.reason
          }],
          summary: {
            totalAssets: 0,
            successfulSweeps: 0,
            failedSweeps: 0,
            skippedSweeps: 1,
            totalAmount: '0'
          }
        };
      }

      console.log(`✅ Wallet ${walletName} is eligible for sweeping. Total eligible amount: ${eligibility.totalEligibleAmount}`);

      /*
      // Step 1: Check and manage gas balance for the target wallet
      const gasCheckResult = await this.checkAndTransferGas(walletId, walletName, dryRun);
      if (gasCheckResult.transferred) {
        console.log(`⛽ Gas transferred to ${walletName}: ${gasCheckResult.amount} ${gasCheckResult.asset}`);
      } else if (gasCheckResult.error) {
        console.log(`⚠️ Gas check warning: ${gasCheckResult.error}`);
      }
        */

      // Process each eligible asset from the eligibility check
      for (const balance of eligibility.assets || []) {
        const asset = balance.asset;
        const amount = balance.value || '0';

        try {
 
            const sweepResult = await this.executeSweep({
              sourceWalletId: walletId,
              destinationWalletId,
              asset,
              amount,
              memo: memo || `Sweep from ${walletName}`
            });

            if (sweepResult.success) {
              results.push({
                walletId,
                walletName,
                asset,
                amount,
                status: 'success',
                transactionId: sweepResult.transactionId
              });
              totalAmount = (parseFloat(totalAmount) + parseFloat(amount)).toString();
            } else {
              results.push({
                walletId,
                walletName,
                asset,
                amount,
                status: 'failed',
                error: sweepResult.error
              });
            }
          
        } catch (error: any) {
          results.push({
            walletId,
            walletName,
            asset,
            amount,
            status: 'failed',
            error: error.message
          });
        }
      }

      // Calculate summary
      const summary = {
        totalAssets: eligibility.assets?.length || 0,
        successfulSweeps: results.filter(r => r.status === 'success').length,
        failedSweeps: results.filter(r => r.status === 'failed').length,
        skippedSweeps: results.filter(r => r.status === 'skipped').length,
        totalAmount
      };

      return {
        success: true,
        message: `Sweep from wallet completed. ${summary.successfulSweeps} successful, ${summary.failedSweeps} failed, ${summary.skippedSweeps} skipped`,
        results,
        summary
      };

    } catch (error: any) {
      console.error('Sweep from wallet failed:', error);
      return {
        success: false,
        message: `Sweep failed: ${error.message}`,
        results: [],
        summary: {
          totalAssets: 0,
          successfulSweeps: 0,
          failedSweeps: 0,
          skippedSweeps: 0,
          totalAmount: '0'
        }
      };
    }
  }

  /**
   * Execute a single sweep transaction
   */
  private async executeSweep(params: {
    sourceWalletId: string;
    destinationWalletId: string;
    asset: string;
    amount: string;
    memo: string;
  }): Promise<{ success: boolean; transactionId?: string; error?: string }> {
    try {
      const { sourceWalletId, destinationWalletId, asset, amount, memo } = params;

      // Get destination wallet address
      const destWalletResponse = await getUserWallet({ walletId: destinationWalletId });
      if (destWalletResponse.error) {
        return {
          success: false,
          error: `Failed to get destination wallet: ${destWalletResponse.error.message}`
        };
      }

      // Get the appropriate address for the asset's network
      const destWallet = destWalletResponse.data.wallet;

      // Extract addresses from wallet details
      const addresses: string[] = [];

      // Add EVM address (works for Ethereum, BSC, Base, etc.)
      if (destWallet.evmDetails?.address) {
        addresses.push(destWallet.evmDetails.address);
      }

      // Add Solana address
      if (destWallet.solanaDetails?.address) {
        addresses.push(destWallet.solanaDetails.address);
      }

      // Add Tron address
      if (destWallet.tronDetails?.address) {
        addresses.push(destWallet.tronDetails.address);
      }

      // Add Bitcoin addresses
      if (destWallet.btcDetails?.btcNetworkDetails) {
        destWallet.btcDetails.btcNetworkDetails.forEach((detail: any) => {
          if (detail.mainAddress) {
            addresses.push(detail.mainAddress);
          }
        });
      }

      if (addresses.length === 0) {
        return {
          success: false,
          error: 'Destination wallet has no addresses'
        };
      }

      // For now, use the first available address
      // TODO: Match address to asset network for better precision
      const destinationAddress = addresses[0];

      const payoutResponse = await initiateSponsoredTransfer({
        destination: destinationAddress,
        asset,
        amount,
        sourceWallet: sourceWalletId,
        note: 'Sweep operation',
        requestId: `sweep_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      });

      if (payoutResponse.error) {
        return {
          success: false,
          error: `Payout failed: ${payoutResponse.error.message}`
        };
      }

      const transactionData = payoutResponse.data;
      const transactionId = transactionData.transaction?.name?.split('/').pop();

      // Log the sweep operation
      await this.logSweepOperation({
        sourceWalletId,
        destinationWalletId,
        asset,
        amount,
        transactionId,
        status: 'initiated'
      });

      return {
        success: true,
        transactionId
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get sweep history
   */
  async getSweepHistory(limit: number = 100): Promise<any[]> {
    try {
      const query = `
        SELECT * FROM sweep_logs 
        ORDER BY created_at DESC 
        LIMIT ${limit}
      `;
      const result = await this.callQuery(query);
      return Array.isArray(result) ? result : [];
    } catch (error: any) {
      console.error('Failed to get sweep history:', error);
      return [];
    }
  }

  /**
   * Get sweep statistics
   */
  async getSweepStats(): Promise<{
    totalSweeps: number;
    totalAmount: string;
    lastSweepDate: string | null;
    successRate: number;
  }> {
    try {
      const statsQuery = `
        SELECT 
          COUNT(*) as total_sweeps,
          SUM(CAST(amount AS DECIMAL(20,8))) as total_amount,
          MAX(created_at) as last_sweep_date,
          SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as successful_sweeps
        FROM sweep_logs
      `;

      const result = await this.callQuery(statsQuery);
      const stats = Array.isArray(result) && result.length > 0 ? result[0] : {};

      return {
        totalSweeps: parseInt(stats.total_sweeps) || 0,
        totalAmount: stats.total_amount || '0',
        lastSweepDate: stats.last_sweep_date,
        successRate: stats.total_sweeps > 0 ? (parseInt(stats.successful_sweeps) / parseInt(stats.total_sweeps)) * 100 : 0
      };
    } catch (error: any) {
      console.error('Failed to get sweep stats:', error);
      return {
        totalSweeps: 0,
        totalAmount: '0',
        lastSweepDate: null,
        successRate: 0
      };
    }
  }

  /**
   * Log sweep operation to database
   */
  private async logSweepOperation(data: {
    sourceWalletId: string;
    destinationWalletId: string;
    asset: string;
    amount: string;
    transactionId?: string;
    status: string;
  }): Promise<void> {
    try {
      const logData = {
        source_wallet_id: data.sourceWalletId,
        destination_wallet_id: data.destinationWalletId,
        asset: data.asset,
        amount: data.amount,
        transaction_id: data.transactionId,
        status: data.status,
        created_at: new Date().toISOString()
      };

      await this.insertData('sweep_logs', logData);
    } catch (error: any) {
      console.error('Failed to log sweep operation:', error);
    }
  }

  /**
   * Check if sweep operation is locked
   */
  private async checkSweepLock(): Promise<boolean> {
    try {
      const lock = await getItem(this.SWEEP_LOCK_KEY);
      return !!lock;
    } catch (error) {
      return false;
    }
  }

  /**
   * Set sweep operation lock
   */
  private async setSweepLock(): Promise<void> {
    try {
      await setItem(this.SWEEP_LOCK_KEY, Date.now().toString());
    } catch (error) {
      console.error('Failed to set sweep lock:', error);
    }
  }

  /**
   * Release sweep operation lock
   */
  private async releaseSweepLock(): Promise<void> {
    try {
      // Note: Redis delete operation would be used here
      // For now, we'll let it expire naturally
    } catch (error) {
      console.error('Failed to release sweep lock:', error);
    }
  }

  /**
   * Update last sweep run timestamp
   */
  private async updateLastSweepRun(): Promise<void> {
    try {
      await setItem(this.SWEEP_CACHE_KEY, Date.now().toString());
    } catch (error) {
      console.error('Failed to update last sweep run:', error);
    }
  }

  /**
   * Get last sweep run timestamp
   */
  async getLastSweepRun(): Promise<number | null> {
    try {
      const timestamp = await getItem(this.SWEEP_CACHE_KEY);
      return timestamp ? parseInt(timestamp) : null;
    } catch (error) {
      return null;
    }
  }
} 