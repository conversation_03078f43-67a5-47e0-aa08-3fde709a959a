import Transactions from "../models/transactions";
import ThirdPartyHandler from "../helpers/ThirdPartyHandler";
import PegaPay from "../intergrations/PegPay";
import MyFX from "../intergrations/MyFX";
import { Steps } from "../helpers/model";
import Honey<PERSON>oin from "../intergrations/HoneyCoin";
import { StatusCodes } from "../intergrations/interfaces";
import { getItem, setItem } from "../helpers/connectRedis";
import Model from "../helpers/model";
import { get } from "../../../admin/src/helpers/httpRequest";

// Interface for polling response format
interface PollingResponse {
    statusCode: number;    // 200, 400, 202, 203
    transStatus: string;   // "SUCCESS", "FAILED", "PENDING", "ONHOLD"
    description: string;   // Human-readable description
    data: any;            // Original response data
}


const thirdPartyHandler = new ThirdPartyHandler();
const pg = new PegaPay();

class PollingPayoutsService extends Model {

    public async checkPendingDirectPayouts() {

        const pendingTransactions: any = await this.callQuery(
            "SELECT * FROM transactions WHERE status = 'PENDING' AND trans_type='PUSH' AND created_at <= DATE_SUB(NOW(), INTERVAL 1 MINUTE)"
        );
        for (const transaction of pendingTransactions) {
            const result = await new Transactions().handlePendingPayoutTransaction(transaction);
        }

        console.log(`Found ${pendingTransactions.length} pending direct payouts to check`);
    }



        public async getPagasusTransaction(transaction: any): Promise<PollingResponse> {
        const trans_id = transaction.trans_id;
         const amount = transaction.amount;
        const pgStatus: any = await new PegaPay().getTransactionDetails(trans_id, "PUSH");
        console.log(`pullRequest`, pgStatus)
        let statusCode = 0
        let description = ""
        let transStatus = ""

        description = await this.mapDescription(pgStatus.description)
        if (pgStatus && typeof pgStatus === 'object' && 'statusCode' in pgStatus) {
            let pgStatusCode = pgStatus.statusCode
            transStatus = this.checkTransactionStatus(pgStatusCode);

            if (pgStatusCode == "0" && description === "SUCCESS") {
                transStatus = "SUCCESS"
                statusCode = 200
                await new Model().saveTransactionLog(trans_id, "SUCCESS", Steps.TRANS_STATUS_CHECK, 200, "Transaction confirmed", pgStatus)
                console.log(`Transaction ${trans_id} completed successfully`);
            } else if (transStatus === "FAILED") {
                statusCode = 400
                await new Model().saveTransactionLog(trans_id, "FAILED", Steps.TRANS_STATUS_CHECK, 400, "Transaction failed", pgStatus)
            } else if (transStatus === "PENDING") {
                statusCode = 202
            } else if (transStatus === "ONHOLD" || transStatus === "RETRY") {
                statusCode = 203
                await new Model().saveTransactionLog(trans_id, "ONHOLD", Steps.TRANS_STATUS_CHECK, 203, "Transaction on hold", pgStatus)
            }
        }
        return {
            statusCode: statusCode,
            transStatus: transStatus,
            description: description,
            data: pgStatus

        }
    }



    public async getHoneyCoinTransactionStatus(transaction: any): Promise<PollingResponse> {
        const trans_id = transaction.trans_id;
        const client_id = transaction.client_id;
        const transactionDetails: any = await this.getHoneyCoinTransaction(trans_id);
        let statusCode = 0
        let description = ""
        let transStatus = ""

        console.log(`Honey coin payout transactionDetails:`, transactionDetails)
        if (transactionDetails) {
            statusCode = transactionDetails.statusCode
            description = transactionDetails.description
            transStatus = transactionDetails.transStatus

            if (statusCode == 200 && description === "SUCCESS") {
                transStatus = "SUCCESS"
                await new Model().saveTransactionLog(trans_id, "SUCCESS", Steps.TRANS_STATUS_CHECK, 200, "Transaction confirmed", transactionDetails?.data)
                console.log(`Transaction ${trans_id} completed successfully`);
            } else if (statusCode === 400 && description === "FAILED") {
                transStatus = "FAILED"
                await new Model().saveTransactionLog(trans_id, "FAILED", Steps.TRANS_STATUS_CHECK, 400, "Transaction failed", transactionDetails?.data)
            } else if (statusCode === 202) {
                transStatus = "PENDING"
            } else if (transStatus === "ONHOLD" || transStatus === "RETRY") {
                await new Model().saveTransactionLog(trans_id, "ONHOLD", Steps.TRANS_STATUS_CHECK, 203, "Transaction on hold", transactionDetails?.data)
            }
        }

        return {
            statusCode: statusCode,
            transStatus: transStatus,
            description: description,
            data: transactionDetails
        }
    }




    public async checkPendingLiquidityRailPayouts() {
        console.log(`checkPendingLiquidityRailPayouts`)
        try {
            const pendingTransactions = await this.selectDataQuery(
                "transactions",
                `status = 'PROCESSING' AND trans_type='PUSH' and service_name='LIQUIDITY_RAIL'`
            );

            console.log(`Found ${pendingTransactions.length} pending direct payouts to check`);

            for (const transaction of pendingTransactions) {
                const result = await new Transactions().handlePendingPayoutTransaction(transaction);
            }
        } catch (error) {
            console.error('Error checking pending Liquidity Rail payouts:', error);
        }
    }
    async checkLiquidityRailPayout(trans_id: any): Promise<PollingResponse> {
        const transinfo = await get(process.env.LIQUIDITY_RAIL_API_URL + `/accounts/transactions/${trans_id}`)
        console.log(`checkLiquidityRailPayout`, trans_id)
        const transaction = transinfo.data
        const status = transaction.status

        let statusCode = 0
        let description = ""
        let transStatus = ""

        if (status === "SUCCESSFUL") {
            statusCode = 200
            description = "Transaction completed successfully"
            transStatus = "SUCCESS"
            await new Model().saveTransactionLog(trans_id, "SUCCESS", Steps.TRANS_STATUS_CHECK, 200, "Transaction confirmed", transaction)
        } else if (status === "FAILED" || status === "EXPIRED") {
            statusCode = 400
            description = "Transaction failed"
            transStatus = "FAILED"
            await new Model().saveTransactionLog(trans_id, "FAILED", Steps.TRANS_STATUS_CHECK, 400, "Transaction failed", transaction)
        } else if (status === "PENDING") {
            statusCode = 202
            description = "Transaction pending"
            transStatus = "PENDING"
        }

        return {
            statusCode: statusCode,
            transStatus: transStatus,
            description: description,
            data: transaction
        }
    }

    async getHoneyCoinTransaction(transactionId: string) {
        try {

            const transaction = await HoneyCoin.getTransaction(transactionId);
            console.log("🔹 HoneyCoin transaction:", transaction)
            if (transaction?.data?.status === "SUCCESSFUL") {
                return this.mapPayoutResponse(200, "SUCCESS", transaction.data)
            } else if (transaction?.data?.status === "FAILED") {
                return this.mapPayoutResponse(400, transaction.data.note || "FAILED", transaction.data)
            } else {
                return this.mapPayoutResponse(202, transaction?.message || "Transaction pending")
            }
        } catch (error: any) {
            console.error("Error getting HoneyCoin transaction:", error);
            return false;
        }
    }
    mapPayoutResponse(statusCode: any, description: any, data: any = null) {
        return {
            statusCode: statusCode,
            description: description,
            data: data
        }
    }

}

export default PollingPayoutsService;