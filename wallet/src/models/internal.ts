import { createWallet, createWalletAddress, getWalletBalances, getFormattedBalances, listWalletsForVault, getFormattedWalletBalances } from "../intergrations/Utilia";
import { getGasWalletId, getColdWalletId, getPayoutWalletId } from '../helpers/walletConfig';

import Model, { WebhookData } from "../helpers/model";
import StellarSdk from "stellar-sdk";
import { v4 as uuidv4 } from "uuid";
import TwoFactorAuthHelper from "../helpers/2fa.helper";
import CryptoJS from "crypto-js";
import jwt from "jsonwebtoken";
import e from "express";
import ThirdPartyHandler from "../helpers/ThirdPartyHandler";
import PegaPay from "../intergrations/PegPay";
const SECRET_KEY = process.env.SECRET_KEY || "your-secret-key";
const pegPay = new PegaPay()

class Internal extends Model {

  constructor() {
    super();
  }


  async threashnofNotification() {

    const usdclimit = 5
    const bnblimit = 0.0001
    const baseLimit = 0.0001
    const usdtLimit = 0.0001

    const bnbBalance = await this.singleWalletBalance("BNB");
    const baseBalance = await this.singleWalletBalance("BASE");
    const usdtBalance = await this.singleWalletBalance("USDT-TRC20");
    const usdcBalance = await this.singleWalletBalance("USDC-BSC");
    const utilaBalance = await this.singleWalletBalance("USDC-ERC20");

    let message = ""

    // Convert string balances to numbers and handle false values
    const bnbBalanceNum = typeof bnbBalance === 'string' ? parseFloat(bnbBalance) : 0;
    const baseBalanceNum = typeof baseBalance === 'string' ? parseFloat(baseBalance) : 0;
    const usdtBalanceNum = typeof usdtBalance === 'string' ? parseFloat(usdtBalance) : 0;
    const usdcBalanceNum = typeof usdcBalance === 'string' ? parseFloat(usdcBalance) : 0;
    const utilaBalanceNum = typeof utilaBalance === 'string' ? parseFloat(utilaBalance) : 0;

    if (bnbBalanceNum < bnblimit) {
      message += "BNB balance is less than the limit";
    }
    if (baseBalanceNum < baseLimit) {
      message += "BASE balance is less than the limit";
    }
    if (usdtBalanceNum < usdtLimit) {
      message += "USDT balance is less than the limit";
    }
    if (usdcBalanceNum < usdclimit) {
      message += "USDC balance is less than the limit";
    }
    if (utilaBalanceNum < usdclimit) {
      message += "Utila balance is less than the limit";
    }

    const emails = ["<EMAIL>", "<EMAIL>"]
    for (const email of emails) {
      this.sendEmail(email, message);
    }
    return true
  }
  async singleWalletBalance(asset: string) {
    const allowedAssets = ["BNB", "USDC-BSC", "BASE-ERC20", "BASE", "USDT-TRC20"]
    if (!allowedAssets.includes(asset)) {
      return false
    }
    const payoutBalances = await getFormattedWalletBalances(getPayoutWalletId());

    for (const balance of payoutBalances) {
      if (balance.ASSET === asset) {
        return balance.BALANCE;
      }
    }
    return false

  }

  async balanceManagement() {
    try {
      const pegPayBalances = await pegPay.getPullBalance()
      const pegPayPushBalances = await pegPay.getPushBalance()
      const pushBalance = pegPayPushBalances.balance
      const pullBalance = pegPayBalances.balance

      const utilaBalances = await getFormattedBalances() || [];

      const gasBalances = await getFormattedWalletBalances(getGasWalletId());
      const coldBalances = await getFormattedWalletBalances(getColdWalletId());
      const payoutBalances = await getFormattedWalletBalances(getPayoutWalletId());

      const balances = {
        utilaBalances: {
          utilaBalances,
          gasBalances,
          coldBalances,
          payoutBalances
        },
        pegPay: {
          pull: pullBalance,
          push: pushBalance
        },
        walletIds: {
          gas: getGasWalletId(),
          cold: getColdWalletId(),
          payout: getPayoutWalletId()
        }
      }
      this.saveBalances("pegPay", "pull", pullBalance)
      this.saveBalances("pegPay", "push", pushBalance)
      this.saveBalances("utila", "gas", gasBalances)
      this.saveBalances("utila", "cold", coldBalances)
      this.saveBalances("utila", "payout", payoutBalances)
      console.log("balances", balances)
      return this.makeResponse(200, "Balances fetched successfully", balances)
    } catch (error: any) {
      console.log("error", error)
      return this.makeResponse(500, "Error fetching balances", error)
    }
  }

  async saveBalances(provider: string, account: any, balance: any): Promise<void> {
    try {
      this.insertData("balances_log", { provider, account, balance: typeof balance === 'string' ? balance : JSON.stringify(balance) })
    } catch (error: any) {
      console.log("error", error)
    }
  }

  async sendCallBack(data: any) {
    try {
      const { clientId, body } = data;
      const webhook = await this.selectDataQuery("webhooks", `client_id = '${clientId}'`);
      if (webhook.length === 0) {
        return this.makeResponse(404, "Webhook not found");
      }

      const webhookData: WebhookData = {
        type: body.type,
        statusCode: body.status,
        message: body.message,
        client_id: clientId,
        trans_type: body.trans_type,
        fee: body.fee,
        timestamp: body.timestamp,
        reference_id: body.reference_id,
        status: body.status,
        amount: body.amount,
        currency: body.currency,
        sender_account: body.sender_account,
        receiver_account: body.receiver_account,
        transaction_id: body.transaction_id,
        meta: body.meta,
        chainInfo: body.chainInfo || null
      }

      await new ThirdPartyHandler().saveWebhook(body.status, clientId, body.trans_id, body.type, body.status, body.message);
      return this.makeResponse(200, "Webhook fetched successfully", webhook);
    } catch (error: any) {
      console.error("Error fetching webhook:", error);
      return this.makeResponse(500, "Error fetching webhook");
    }
  }


  async getWalletInfo(clientId: string) {
    const supportedNetworks: any = await this.callQuery(`select * from utilia_networks where is_muda_supported=1 `);
    let walletInfo: any = null;
    const walletInfoArray: any = [];



    for (const network of supportedNetworks) {
      const chain = (network.name).replace("networks/", "")
      walletInfoArray.push(chain)
    }
    walletInfo = await createWallet({
      name: clientId,
      networks: walletInfoArray
    })
    console.log("walletInfo1", walletInfo)
    if (walletInfo.data.code == 200) {
      walletInfo = walletInfo.data.wallet;
    } else if (walletInfo.data.code == 6) {
      const walletsArray = await listWalletsForVault({
        name: clientId,
      })
      console.log("wallets", walletsArray)
      walletInfo = walletsArray.data.wallets[0];
      console.log("walletInfo77", walletInfo)
    }
    return walletInfo;
  }

  async generateDepositAddress(data: any) {
    try {
      const { clientId, tag } = data;
      const client = await this.selectDataQuery("clients", `client_id = '${clientId}'`);
      if (client.length === 0) {
        return this.makeResponse(404, "Client not found");
      }
      const depositAddress: any = await this.callQuery(`select * from dp_crypto_deposits where client_id = '${clientId}'`);
      let hasWallet = false;
      if (depositAddress.length > 0) {
        hasWallet = true;
        //  return this.makeResponse(200, "Deposit address already exists", depositAddress[0].address);
      }
      const supportedNetworks: any = await this.callQuery(`select * from utilia_networks where is_muda_supported=1 `);


      const walletInfo = await this.getWalletInfo(clientId);
      console.log("walletInfo", walletInfo)

      const name = walletInfo.name;
      const vaultId = name.split("/")[1];
      const walletId = name.split("/")[3] || "";


      const tronDetails = walletInfo.tronDetails.address
      const evmDetails = walletInfo.evmDetails.address


      const tronDepositAddressData = {
        client_id: clientId,
        deposit_address: tronDetails,
        tag: clientId,
        network: "tron",
        vault_id: vaultId
      }


      const evmDepositAddressData = {
        client_id: clientId,
        deposit_address: evmDetails,
        tag: clientId,
        network: "evm",
        vault_id: vaultId
      }
      try {
        await this.insertData("dp_crypto_deposits", evmDepositAddressData);
      } catch (error: any) {
        console.log("error", error)
      }

      try {
        await this.insertData("dp_crypto_deposits", tronDepositAddressData);
      } catch (error: any) {
        console.log("error", error)
      }

      return this.makeResponse(200, "Deposit address generated successfully");
      const depositAddressData: any = [];
      for (const network of supportedNetworks) {
        const chain = network.name;

        const addressInfo: any = await createWalletAddress({
          network: chain,
          walletId: walletId
        })
        console.log("addressInfo", addressInfo)
        const address = addressInfo.data.address;
        const displayName = addressInfo.data.displayName;

        const depositAddressData = {
          client_id: clientId,
          currency: "USDT",
          deposit_address: address,
          tag: displayName,
          chain,
          vault_id: vaultId
        }
        await this.insertData("dp_crypto_deposits", depositAddressData);
      }
      return this.makeResponse(200, "Deposit address generated successfully", depositAddressData);
    } catch (error: any) {
      console.log("error", error)
      return this.makeResponse(500, "error");
    }
  }
}

export default Internal;