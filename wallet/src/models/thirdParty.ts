import Model, { Steps } from "../helpers/model";
import { v4 as uuidv4 } from "uuid";
import { symbol, z } from "zod";
import dotenv from "dotenv";
import crypto from "crypto";
import { query } from "express";
import Decimal from 'decimal.js';
import HoneyCoin from "../intergrations/HoneyCoin";
import Transactions from "./transactions";
import { StatusCodes } from "../intergrations/interfaces";
const transactions = new Transactions();

class ThirdParty extends Model {

    private honeycoin: typeof HoneyCoin;
    constructor() {
        super();
        this.honeycoin = HoneyCoin;
    }





    async sendHonryCoinWebhook(data: any) {
        try {
            
            //array of pending collection transactions
            let pendingCollectionTransactions = [];
            // Check if order exists and is available
            const existingOrder = await this.selectDataQuery(
                "transactions", 
                `trans_type = 'PULL' AND status = 'INITIATED' AND currency = 'KES' AND service_name = 'MPESA_COLLECTION'`
            );  

            if (existingOrder.length > 0) {
               pendingCollectionTransactions = existingOrder.map((item: any) => item.trans_id);
            }

            console.log("🔹 Pending collection honey coin webhooks -----------100000--------> :", data)
            if (data.success && data.data && Array.isArray(data.data)) {
                for (const webhookItem of data.data) {
                    if (webhookItem.data) {

                        const { status, type, transactionId, externalReference } = webhookItem.data;
                        //  check if externalReference is in pendingCollectionTransactions
                        if (status === "success" && type === "deposit" && pendingCollectionTransactions.includes(externalReference)) {
                            console.log(`Processing successful deposit for transaction: ${transactionId}`);
                            const result = await transactions.issueTokens(externalReference);
                            console.log(`Token issuance result for ${transactionId}:`, result);
                        } 
                        
                    }
                }
            }
            return this.makeResponse(StatusCodes.SUCCESS.code, "Webhook processed successfully", {});

        } catch (error: any) {
            console.error("Error sending HonryCoin webhook:", error);
            throw new Error("Failed to send HonryCoin webhook");
        }
    }







    

}

export default ThirdParty;

