import axios, { AxiosResponse } from 'axios';
import { v4 as uuidv4 } from 'uuid';
import crypto from 'crypto';
import dotenv from 'dotenv';

dotenv.config();

// Tembo API Response Interfaces
interface TemboBaseResponse {
  success?: boolean;
  message?: string;
  statusCode?: string | number;
  reason?: string;
}

interface TemboVirtualAccountResponse extends TemboBaseResponse {
  id?: string;
  accountName?: string;
  accountNo?: string;
  reference?: string;
  tag?: string;
}

interface TemboBalanceResponse extends TemboBaseResponse {
  result?: {
    accountNo: string;
    accountName: string;
    branchCode: string;
    availableBalance: number;
    bookedBalance: number;
  };
}

interface TemboStatementResponse extends TemboBaseResponse {
  result?: {
    accountNo: string;
    statement: Array<{
      id: string;
      transactionId: string;
      reference: string;
      transactionType: string;
      channel: string;
      transactionDate: string;
      postingDate: string;
      valueDate: string;
      narration: string;
      currency: string;
      amountCredit: number;
      amountDebit: number;
      clearedBalance: number;
      bookedBalance: number;
    }>;
  };
}

interface TemboCollectionResponse extends TemboBaseResponse {
  transactionRef?: string;
  transactionId?: string;
}

interface TemboPaymentResponse extends TemboBaseResponse {
  transactionRef?: string;
  transactionId?: string;
}

// Webhook payload interface
interface TemboWebhookPayload {
  accountNo: string;
  payerName?: string;
  id: string;
  transactionId: string;
  reference: string;
  transactionType: string;
  channel: string;
  transactionDate: string;
  postingDate: string;
  valueDate: string;
  narration: string;
  currency: string;
  amountCredit: number;
  amountDebit: number;
  clearedBalance: number;
  bookedBalance: number;
}

// Standard response format following existing patterns
interface StandardResponse {
  status: number;
  message?: string;
  trans_id?: string;
  amount_transfered?: number;
  transaction_fee?: number;
  data?: any;
}

// Logger interface for optional logging
interface Logger {
  info(message: string, data?: any): void;
  error(message: string, data?: any): void;
  warn(message: string, data?: any): void;
}

// Simple console logger implementation
const defaultLogger: Logger = {
  info: (message: string, data?: any) => console.log(`INFO: ${message}`, data || ''),
  error: (message: string, data?: any) => console.error(`ERROR: ${message}`, data || ''),
  warn: (message: string, data?: any) => console.warn(`WARN: ${message}`, data || '')
};

/**
 * TemboService - Comprehensive wrapper for Tembo API endpoints
 * Independent implementation without internal dependencies
 */
export default class TemboService {
  private baseURL: string;
  private authToken: string;
  private accountId: string;
  private secretKey: string;
  private webhookSecret: string;
  private logger: Logger;

  constructor(logger?: Logger) {
    this.logger = logger || defaultLogger;

    // Define required environment variables with their descriptions
    const requiredEnvVars = [
      { key: 'TEMBO_AUTH_TOKEN', description: 'Tembo API authentication token' },
      { key: 'TEMBO_ACCOUNT_ID', description: 'Tembo account identifier' },
      { key: 'TEMBO_SECRET_KEY', description: 'Tembo API secret key (also used for webhook verification)' },
      { key: 'WEBHOOK_BASE_URL', description: 'Base URL for webhook callbacks' }
    ];

    // Validate all required environment variables
    this.validateRequiredEnvVars(requiredEnvVars);

    // Set configuration values
    this.baseURL = process.env.TEMBO_API_URL || 'https://sandbox.temboplus.com';
    this.authToken = process.env.TEMBO_AUTH_TOKEN!;
    this.accountId = process.env.TEMBO_ACCOUNT_ID!;
    this.secretKey = process.env.TEMBO_SECRET_KEY!;
    this.webhookSecret = process.env.TEMBO_SECRET_KEY!; // Use same secret key for webhooks
  }

  /**
   * Validate required environment variables
   * @param requiredVars Array of required environment variable configurations
   */
  private validateRequiredEnvVars(requiredVars: Array<{ key: string; description: string }>): void {
    const missingVars: string[] = [];

    for (const envVar of requiredVars) {
      if (!process.env[envVar.key]) {
        missingVars.push(`${envVar.key} (${envVar.description})`);
      }
    }

    if (missingVars.length > 0) {
      throw new Error(
        `Missing required environment variables for Tembo integration:\n` +
        missingVars.map(varInfo => `  - ${varInfo}`).join('\n') +
        `\n\nPlease configure these environment variables before using TemboService.`
      );
    }
  }

  /**
   * Generate request headers following Tembo API requirements
   */
  private getHeaders(includeAuth: boolean = true): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'x-request-id': uuidv4()
    };

    if (includeAuth) {
      if (this.authToken) {
        headers['Authorization'] = `Bearer ${this.authToken}`;
      }
      if (this.accountId) {
        headers['x-account-id'] = this.accountId;
      }
      if (this.secretKey) {
        headers['x-secret-key'] = this.secretKey;
      }
    }

    return headers;
  }

  /**
   * Sanitize headers for logging (remove sensitive information)
   */
  private sanitizeHeaders(headers: Record<string, string>): Record<string, string> {
    const sanitized = { ...headers };
    
    // Mask sensitive headers
    if (sanitized['Authorization']) {
      sanitized['Authorization'] = 'Bearer ***MASKED***';
    }
    if (sanitized['x-secret-key']) {
      sanitized['x-secret-key'] = '***MASKED***';
    }
    
    return sanitized;
  }

  /**
   * Make HTTP request with error handling
   */
  private async makeRequest(
    method: 'GET' | 'POST',
    endpoint: string,
    data?: any,
    useCollectionAuth: boolean = false,
    useBearerAuth: boolean = false
  ): Promise<any> {
    const requestId = `REQ_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
    const startTime = Date.now();
    
    try {
      const url = `${this.baseURL}${endpoint}`;
      const headers = useBearerAuth ?
        this.getBearerHeaders() :
        (useCollectionAuth ?
          this.getCollectionHeaders() :
          this.getHeaders());

      // Enhanced request logging
      this.logger.info(`🔹 Tembo ${method} Request [${requestId}]`, { 
        url, 
        endpoint,
        method,
        headers: this.sanitizeHeaders(headers),
        payload: data,
        authType: useBearerAuth ? 'Bearer' : (useCollectionAuth ? 'Collection' : 'Standard'),
        timestamp: new Date().toISOString()
      });

      let response: AxiosResponse;
      
      if (method === 'POST') {
        response = await axios.post(url, data, { headers });
      } else {
        response = await axios.get(url, { headers });
      }

      const duration = Date.now() - startTime;

      // Enhanced response logging
      this.logger.info(`✅ Tembo Response [${requestId}]`, { 
        status: response.status,
        statusText: response.statusText,
        duration: `${duration}ms`,
        headers: response.headers,
        data: response.data,
        endpoint,
        method,
        timestamp: new Date().toISOString()
      });

      return response.data;
    } catch (error: any) {
      const duration = Date.now() - startTime;
      
      // Enhanced error logging
      this.logger.error(`❌ Tembo API Error [${requestId}]`, {
        endpoint,
        method,
        duration: `${duration}ms`,
        error: {
          message: error.message,
          code: error.code,
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
          headers: error.response?.headers
        },
        request: {
          url: `${this.baseURL}${endpoint}`,
          payload: data
        },
        timestamp: new Date().toISOString()
      });

      // Return standardized error response
      return {
        success: false,
        statusCode: error.response?.status || 500,
        message: error.response?.data?.message || error.message,
        data: error.response?.data
      };
    }
  }

  /**
   * Get collection-specific headers (different auth pattern)
   */
  private getCollectionHeaders(): Record<string, string> {
    return {
      'Content-Type': 'application/json',
      'x-account-id': this.accountId,
      'x-secret-key': this.secretKey,
      'x-request-id': uuidv4()
    };
  }

  /**
   * Get Bearer token headers for remittance API
   */
  private getBearerHeaders(): Record<string, string> {
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.authToken}`,
      'x-request-id': uuidv4()
    };
  }

  /**
   * Normalize response to standard format
   */
  private normalizeResponse(
    temboResponse: any, 
    transactionId?: string,
    amount?: number
  ): StandardResponse {
    // Check if it's an error response first
    if (temboResponse.success === false || 
        (temboResponse.statusCode && typeof temboResponse.statusCode === 'number' && temboResponse.statusCode >= 400)) {
      return {
        status: typeof temboResponse.statusCode === 'number' ? 
          temboResponse.statusCode : 500,
        message: temboResponse.message || temboResponse.reason || 'Transaction failed',
        data: temboResponse
      };
    }

    // Success cases - if we have actual data or explicit success indicators
    if (temboResponse.success === true || 
        temboResponse.statusCode === 'PENDING_ACK' || 
        temboResponse.statusCode === 'PAYMENT_ACCEPTED' ||
        temboResponse.statusCode === 201 ||
        // For balance/statement endpoints that return data directly
        (temboResponse.accountNo && temboResponse.accountName) ||
        (temboResponse.availableBalance !== undefined) ||
        (temboResponse.currentBalance !== undefined) ||
        // For transaction endpoints
        temboResponse.transactionRef ||
        temboResponse.transactionId ||
        // If response has data but no explicit error indicators, assume success
        (!temboResponse.statusCode && Object.keys(temboResponse).length > 0)) {
      return {
        status: 200,
        message: temboResponse.message || 'Request successful',
        trans_id: temboResponse.transactionRef || temboResponse.transactionId || transactionId,
        amount_transfered: amount,
        data: temboResponse
      };
    }

    // Default error case
    return {
      status: 500,
      message: temboResponse.message || temboResponse.reason || 'Unknown response format',
      data: temboResponse
    };
  }

  /**
   * Detect mobile network from phone number
   */
  private detectMobileNetwork(phone: string): string {
    const cleanPhone = phone.replace(/\+/g, '');
    
    // Tanzania mobile prefixes (more comprehensive check)
    const prefix = cleanPhone.substring(0, 6); // Get first 6 digits: 255XXX
    
    // Vodacom M-Pesa prefixes
    if (prefix === '255757' || prefix === '255756' || prefix === '255755' || prefix === '255754') {
      return 'TZ-VODACOM-C2B'; // M-Pesa
    }
    
    // Airtel Money prefixes
    if (prefix === '255789' || prefix === '255788' || prefix === '255787' || prefix === '255786' || prefix === '255785' || prefix === '255784' || prefix === '255783' || prefix === '255782') {
      return 'TZ-AIRTEL-C2B';
    }
    
    // Tigo Pesa prefixes
    if (prefix === '255715' || prefix === '255714' || prefix === '255713' || prefix === '255712' || prefix === '255711') {
      return 'TZ-TIGO-C2B';
    }
    
    // Unknown prefix - return empty to indicate invalid
    return '';
  }

  /**
   * Get payout network code
   */
  private getPayoutNetwork(phone: string): string {
    const cleanPhone = phone.replace(/\+/g, '');
    const prefix = cleanPhone.substring(0, 6); // Get first 6 digits: 255XXX
    
    // Vodacom M-Pesa prefixes
    if (prefix === '255757' || prefix === '255756' || prefix === '255755' || prefix === '255754') {
      return 'TZ-VODACOM-B2C';
    }
    
    // Airtel Money prefixes
    if (prefix === '255789' || prefix === '255788' || prefix === '255787' || prefix === '255786' || prefix === '255785' || prefix === '255784' || prefix === '255783' || prefix === '255782') {
      return 'TZ-AIRTEL-B2C';
    }
    
    // Tigo Pesa prefixes
    if (prefix === '255715' || prefix === '255714' || prefix === '255713' || prefix === '255712' || prefix === '255711') {
      return 'TZ-TIGO-B2C';
    }

    // Default to empty for unknown numbers
    return '';
  }

  // ==================== VIRTUAL ACCOUNT METHODS ====================

  /**
   * Create Virtual Account for merchant
   * Maps to: POST /account
   */
  async createVirtualAccount(
    merchantId: string,
    metadata: { companyName: string; tag?: string }
  ): Promise<StandardResponse> {
    const transId = `VA_${merchantId}_${Date.now()}`;

    try {
      const payload = {
        companyName: metadata.companyName,
        reference: `MUDA_${merchantId}_${Date.now()}`,
        tag: metadata.tag || process.env.TEMBO_DEFAULT_TAG || 'MUDA'
      };

      const response: TemboVirtualAccountResponse = await this.makeRequest(
        'POST',
        '/account',
        payload
      );

      return this.normalizeResponse(response, transId);
    } catch (error: any) {
      throw error;
    }
  }

  /**
   * Get Virtual Account Balance
   * Maps to: POST /account/balance
   */
  async getVirtualAccountBalance(accountNo: string): Promise<StandardResponse> {
    const transId = `BAL_${accountNo}_${Date.now()}`;

    try {
      const payload = { accountNo };
      const response: TemboBalanceResponse = await this.makeRequest(
        'POST',
        '/account/balance',
        payload
      );

      return this.normalizeResponse(response, transId);
    } catch (error: any) {
      throw error;
    }
  }

  /**
   * Get Virtual Account Statement
   * Maps to: POST /account/statement
   */
  async getVirtualAccountStatement(
    accountNo: string,
    startDate: string,
    endDate: string
  ): Promise<StandardResponse> {
    const transId = `STMT_${accountNo}_${Date.now()}`;

    try {
      const payload = { accountNo, startDate, endDate };
      const response: TemboStatementResponse = await this.makeRequest(
        'POST',
        '/account/statement',
        payload
      );

      return this.normalizeResponse(response, transId);
    } catch (error: any) {
      throw error;
    }
  }

  // ==================== COLLECTION METHODS ====================

  /**
   * Mobile Money Pull Request (Collection)
   * Maps to: POST /tembo/v1/collection
   *
   * CRITICAL: Collections go to Muda's collection account, NOT individual merchant accounts.
   * After webhook is received, funds must be transferred to merchant virtual account.
   */
  async makeMMPullRequest(
    userId: string,
    VendorTranId: string,
    TranAmount: string,
    phone: string
  ): Promise<StandardResponse> {
    try {
      const amount = parseFloat(TranAmount);
      const currency = 'TZS'; // Default to Tanzanian Shilling

      // Auto-detect network from phone number
      const channel = this.detectMobileNetwork(phone);
      const cleanPhone = phone.replace(/\+/g, '');

      const payload = {
        msisdn: cleanPhone,
        channel: channel,
        amount: amount,
        narration: `Collection for ${VendorTranId}`,
        // CRITICAL: This transactionRef is how we track which merchant the funds belong to
        transactionRef: VendorTranId,  // ← This maps back to transactions table client_id
        transactionDate: new Date().toISOString().slice(0, 19).replace('T', ' '),
        callbackUrl: `${process.env.WEBHOOK_BASE_URL}/webhook/tembo`
      };

      const response: TemboCollectionResponse = await this.makeRequest(
        'POST',
        '/tembo/v1/collection',
        payload,
        true // Use collection auth headers
      );

      return this.normalizeResponse(response, VendorTranId, amount);
    } catch (error: any) {
      throw error;
    }
  }

  // ==================== PAYOUT METHODS ====================

  /**
   * Mobile Money Push Request (Payout)
   * Maps to: POST /tembo/v1/payment/wallet-to-mobile
   *
   * All payouts are made from main account - no virtual accounts needed.
   */
  async makeMMPushRequest(
    userId: string,
    VendorTranId: string,
    TranAmount: string,
    phone: string
  ): Promise<StandardResponse> {
    try {
      const amount = parseFloat(TranAmount);
      const currency = 'TZS'; // Default to Tanzanian Shilling

      // Auto-detect network for payout
      const serviceCode = this.getPayoutNetwork(phone);
      const cleanPhone = phone.replace(/\+/g, '');

      const payload = {
        countryCode: 'TZ',
        serviceCode: serviceCode,
        accountNo: '**********',
        amount: amount,
        msisdn: cleanPhone,
        narration: `Payout for ${VendorTranId}`,
        currencyCode: currency,
        recipientNames: 'Customer',
        transactionRef: VendorTranId,
        transactionDate: new Date().toISOString().slice(0, 19).replace('T', ' '),
        callbackUrl: `${process.env.WEBHOOK_BASE_URL}/webhook/tembo`
      };

      const response: TemboPaymentResponse = await this.makeRequest(
        'POST',
        '/tembo/v1/payment/wallet-to-mobile',
        payload,
        true
      );

      return this.normalizeResponse(response, VendorTranId, amount);
    } catch (error: any) {
      throw error;
    }
  }

  /**
   * Bank Transfer from Main Account
   * Maps to: POST /tembo/v1/payment/wallet-to-mobile (with bank service code)
   *
   * All bank transfers are made from main account - no virtual accounts needed.
   */
  async payToBank(
    userId: string,
    VendorTranId: string,
    TranAmount: string,
    bankCode: string,
    accountNumber: string,
    recipientName: string
  ): Promise<StandardResponse> {
    try {
      const amount = parseFloat(TranAmount);
      const currency = 'TZS';

      const payload = {
        countryCode: 'TZ',
        accountNo: '**********',
        serviceCode: 'TZ-BANK-B2C', // Bank transfer service code
        amount: amount,
        msisdn: `${bankCode}:${accountNumber}`, // Bank format: BIC:ACCOUNT_NUMBER
        narration: `Bank transfer for ${VendorTranId}`,
        currencyCode: currency,
        recipientNames: recipientName,
        transactionRef: VendorTranId,
        transactionDate: new Date().toISOString().slice(0, 19).replace('T', ' '),
        callbackUrl: `${process.env.WEBHOOK_BASE_URL}/webhook/tembo`
      };

      const response: TemboPaymentResponse = await this.makeRequest(
        'POST',
        '/tembo/v1/payment/wallet-to-mobile', // Same endpoint, different service code
        payload,
        true
      );

      return this.normalizeResponse(response, VendorTranId, amount);
    } catch (error: any) {
      throw error;
    }
  }

  // ==================== BALANCE & STATEMENT METHODS ====================

  /**
   * Get Main Account Balance
   * Maps to: POST /tembo/v1/wallet/main-balance
   */
  async getMainBalance(): Promise<StandardResponse> {
    const transId = `MAIN_BAL_${Date.now()}`;

    try {
      const response = await this.makeRequest(
        'POST',
        '/tembo/v1/wallet/main-balance',
        {}, // Empty body as per Tembo docs
        true
      );

      return this.normalizeResponse(response, transId);
    } catch (error: any) {
      throw error;
    }
  }

  /**
   * Get Collection Balance
   * Maps to: POST /tembo/v1/wallet/collection-balance
   */
  async getCollectionBalance(): Promise<StandardResponse> {
    const transId = `COL_BAL_${Date.now()}`;

    try {
      const response = await this.makeRequest(
        'POST',
        '/tembo/v1/wallet/collection-balance',
        {}, // Empty body as per Tembo docs
        true
      );

      return this.normalizeResponse(response, transId);
    } catch (error: any) {
      throw error;
    }
  }

  /**
   * Get Collection Statement
   * Maps to: POST /tembo/v1/wallet/collection-statement
   */
  async getCollectionStatement(startDate: string, endDate: string): Promise<StandardResponse> {
    const transId = `COL_STMT_${Date.now()}`;

    try {
      const payload = {
        startDate: startDate, // Format: YYYY-MM-DD
        endDate: endDate     // Format: YYYY-MM-DD
      };

      const response = await this.makeRequest(
        'POST',
        '/tembo/v1/wallet/collection-statement',
        payload,
        true
      );

      return this.normalizeResponse(response, transId);
    } catch (error: any) {
      throw error;
    }
  }

  // ==================== OFF-RAMP METHODS ====================

  /**
   * Get Off-Ramp Exchange Rate for crypto-to-TZS
   * Uses external rate sources since Tembo doesn't have crypto rates
   */
  async getOffRampRate(
    fromCurrency: string, // e.g., 'USDC', 'BTC', 'ETH'
    toCurrency: string = 'TZS',
    amount: number
  ): Promise<any> {
    const transId = `OFFRAMP_RATE_${Date.now()}`;

    try {
      // Note: This would need external rate service integration
      // For now, return a mock response structure
      const mockRate = 2500; // Mock TZS rate
      const fiatAmount = amount * mockRate;
      const estimatedFee = fiatAmount * 0.02;

      return {
        provider: "tembo_external",
        providerQuoteId: transId,
        from: fromCurrency,
        providerId: 'tembo_tz',
        to: toCurrency,
        fiatAmount: Math.round(fiatAmount - estimatedFee),
        toAmount: Math.round(fiatAmount - estimatedFee),
        cryptoAmount: amount,
        fee: Math.round(estimatedFee),
        quoteId: transId,
        expiresAt: new Date(Date.now() + 10 * 60 * 1000).toISOString(), // 10 minutes
        quotedPrice: mockRate.toString(),
        exchangeRate: mockRate,
        note: "Mock rate - integrate with external rate service"
      };

    } catch (error: any) {
      throw error;
    }
  }

  /**
   * Execute TZS Disbursement (final step of off-ramp)
   * This handles the TZS disbursement after crypto has been converted to fiat
   * Uses Tembo's mobile money/bank transfer capabilities
   */
  async executeTzsDisbursement(
    transactionId: string,
    disbursementData: {
      amount: number; // TZS amount to disburse
      destination: 'MOBILE' | 'BANK';
      payoutMethod: {
        accountNumber: string; // Phone number or bank account
        accountName: string;
        institutionCode: string; // VODACOM, TIGO, AIRTEL, or bank code
        branchCode?: string; // For bank transfers
      };
      narration: string;
    }
  ): Promise<StandardResponse> {
    try {
      let response: StandardResponse;

      if (disbursementData.destination === 'MOBILE') {
        // Use mobile money payout
        response = await this.makeMMPushRequest(
          'offramp_user',
          transactionId,
          disbursementData.amount.toString(),
          disbursementData.payoutMethod.accountNumber
        );
      } else {
        // Use bank transfer
        response = await this.payToBank(
          'offramp_user',
          transactionId,
          disbursementData.amount.toString(),
          disbursementData.payoutMethod.institutionCode,
          disbursementData.payoutMethod.accountNumber,
          disbursementData.payoutMethod.accountName
        );
      }

      return response;

    } catch (error: any) {
      throw error;
    }
  }

  // ==================== REMITTANCE METHODS ====================

  /**
   * Create Remittance
   * Maps to: POST /remittance
   * For international money transfers to Tanzania
   */
  async createRemittance(
    transactionId: string,
    remittanceData: {
      senderCurrency: string;
      senderAmount: number;
      receiverCurrency: string;
      receiverAmount: number;
      exchangeRate: number;
      receiverAccount: string; // Phone number or bank account
      receiverChannel: 'MOBILE' | 'BANK';
      institutionCode: string; // VODACOM, TIGO, CRDB, etc.
      sender: {
        fullName: string;
        nationality: string;
        countryCode: string;
        idType: string;
        idNumber: string;
        idExpiryDate: string;
        dateOfBirth: string;
        phoneNumber: string;
        email: string;
        address: string;
        sourceOfFundsDeclaration: string;
        purposeOfTransaction: string;
        occupation: string;
        employer: string;
      };
      receiver: {
        fullName: string;
        phoneNumber: string;
        email?: string;
        countryCode: string;
      };
    }
  ): Promise<StandardResponse> {
    try {
      const payload = {
        paymentDate: new Date().toISOString(),
        senderCurrency: remittanceData.senderCurrency,
        senderAmount: remittanceData.senderAmount,
        receiverCurrency: remittanceData.receiverCurrency,
        receiverAmount: remittanceData.receiverAmount,
        exchangeRate: remittanceData.exchangeRate,
        receiverAccount: remittanceData.receiverAccount,
        receiverChannel: remittanceData.receiverChannel,
        institutionCode: remittanceData.institutionCode,
        partnerReference: transactionId,
        callbackUrl: `${process.env.WEBHOOK_BASE_URL}/webhook/tembo/remittance`,
        sender: remittanceData.sender,
        receiver: remittanceData.receiver
      };

      const response = await this.makeRequest(
        'POST',
        '/remittance',
        payload,
        true,
        true // Use Bearer token for remittance
      );

      return this.normalizeResponse(response, transactionId, remittanceData.receiverAmount);
    } catch (error: any) {
      throw error;
    }
  }

  /**
   * Get Remittance Status
   * Maps to: GET /remittance/status/{partnerReference}
   */
  async getRemittanceStatus(partnerReference: string): Promise<StandardResponse> {
    const transId = `REM_STATUS_${Date.now()}`;

    try {
      const response = await this.makeRequest(
        'GET',
        `/remittance/status/${partnerReference}`,
        undefined,
        true,
        true // Use Bearer token for remittance
      );

      return this.normalizeResponse(response, transId);
    } catch (error: any) {
      throw error;
    }
  }

  // ==================== TRANSACTION STATUS METHODS ====================

  /**
   * Get Transaction Status
   * Maps to: GET /tembo/v1/payment/status/{transactionId} or similar
   */
  async getTransactionStatus(transactionRef: string): Promise<StandardResponse> {
    const transId = `STATUS_${transactionRef}_${Date.now()}`;

    try {
      // Note: Exact endpoint may vary - check Tembo docs
      const response = await this.makeRequest(
        'GET',
        `/tembo/v1/payment/status/${transactionRef}`,
        undefined,
        true
      );

      return this.normalizeResponse(response, transId);
    } catch (error: any) {
      throw error;
    }
  }

  // ==================== WEBHOOK METHODS ====================

  /**
   * Verify webhook signature
   * Follows Tembo's HMAC SHA-256 signature verification
   */
  verifyWebhookSignature(
    payload: TemboWebhookPayload,
    timestamp: string,
    receivedSignature: string
  ): boolean {
    try {
      if (!this.webhookSecret) {
        this.logger.warn('Webhook secret not configured');
        return false;
      }

      // Decode the secret from base64
      const secret = Buffer.from(this.webhookSecret, 'base64');

      // Reconstruct the concatenated string as per Tembo docs
      const concatenatedString =
        timestamp +
        payload.accountNo +
        payload.id +
        payload.transactionId +
        payload.reference +
        payload.transactionType +
        payload.channel +
        payload.transactionDate +
        payload.postingDate +
        payload.valueDate +
        payload.narration +
        payload.currency +
        Math.trunc(payload.amountCredit).toString() +
        Math.trunc(payload.amountDebit).toString() +
        Math.trunc(payload.clearedBalance).toString() +
        Math.trunc(payload.bookedBalance).toString();

      // Compute HMAC signature
      const hmac = crypto.createHmac('sha256', secret);
      hmac.update(Buffer.from(concatenatedString, 'utf-8'));
      const computedSignature = hmac.digest('base64');

      return computedSignature === receivedSignature;
    } catch (error: any) {
      this.logger.error('Webhook signature verification failed', { error: error.message });
      return false;
    }
  }

  /**
   * Process webhook payload
   * Normalizes webhook data to standard format
   *
   * CRITICAL: This webhook indicates funds have been received in Tembo's main collection account.
   * The payload.reference contains the transactionRef that maps back to the merchant's transaction.
   */
  async processWebhook(
    payload: TemboWebhookPayload,
    signature: string,
    timestamp: string
  ): Promise<StandardResponse> {
    try {
      // Verify signature
      const isValid = this.verifyWebhookSignature(payload, timestamp, signature);
      if (!isValid) {
        return {
          status: 401,
          message: 'Invalid webhook signature',
          data: payload
        };
      }

      // Determine transaction type and amount based on Tembo webhook structure
      // Both amountCredit and amountDebit can be present in the same transaction
      const creditAmount = payload.amountCredit || 0;
      const debitAmount = payload.amountDebit || 0;

      // Determine the primary transaction direction
      let transactionDirection: 'CREDIT' | 'DEBIT' | 'TRANSFER';
      let primaryAmount: number;

      if (creditAmount > 0 && debitAmount === 0) {
        // Pure credit transaction (money coming in)
        transactionDirection = 'CREDIT';
        primaryAmount = creditAmount;
      } else if (debitAmount > 0 && creditAmount === 0) {
        // Pure debit transaction (money going out)
        transactionDirection = 'DEBIT';
        primaryAmount = debitAmount;
      } else if (creditAmount > 0 && debitAmount > 0) {
        // Transfer or transaction with fees
        transactionDirection = 'TRANSFER';
        primaryAmount = Math.max(creditAmount, debitAmount); // Use larger amount
      } else {
        // Both are zero - unusual case
        transactionDirection = 'CREDIT';
        primaryAmount = 0;
      }

      // Use Tembo's actual transaction type and channel for more context
      const temboTransactionType = payload.transactionType; // e.g., "H4"
      const channel = payload.channel; // e.g., "CMM" for mobile money

      // CRITICAL: For collections (credits), this means a customer paid into Tembo's main account
      // The payload.reference is the transactionRef we sent, which maps to our transactions table
      // This allows us to identify which merchant the funds belong to

      if (transactionDirection === 'CREDIT') {
        this.logger.info(`💰 Collection received in Tembo main account`, {
          reference: payload.reference,
          creditAmount: creditAmount,
          debitAmount: debitAmount,
          primaryAmount: primaryAmount,
          channel: payload.channel,
          payer: payload.payerName,
          temboTransactionType: temboTransactionType,
          note: 'Funds need to be attributed to merchant based on reference'
        });
      }

      // Return standardized webhook data that follows existing webhook patterns
      return {
        status: 200,
        message: 'Tembo webhook processed - merchant attribution via reference',
        trans_id: payload.reference,  // ← This maps back to transactions.trans_id
        amount_transfered: primaryAmount,
        data: {
          ...payload,
          transaction_direction: transactionDirection,
          primary_amount: primaryAmount,
          credit_amount: creditAmount,
          debit_amount: debitAmount,
          tembo_transaction_type: temboTransactionType,
          channel: channel,
          is_collection: transactionDirection === 'CREDIT',
          merchant_attribution_note: 'Use payload.reference to find merchant in transactions table',
          processed_at: new Date().toISOString()
        }
      };
    } catch (error: any) {
      this.logger.error('Tembo webhook processing failed', { error: error.message, payload });
      return {
        status: 500,
        message: 'Tembo webhook processing failed',
        data: { error: error.message, payload }
      };
    }
  }

  // ==================== UTILITY METHODS ====================

  /**
   * Validate phone number format for Tanzania
   */
  validatePhoneNumber(phone: string): { valid: boolean; message?: string } {
    const cleanPhone = phone.replace(/[\s\-\+]/g, '');

    // Tanzania phone numbers should start with 255 and be 12 digits
    if (!cleanPhone.startsWith('255')) {
      return { valid: false, message: 'Phone number must start with 255 (Tanzania country code)' };
    }

    if (cleanPhone.length !== 12) {
      return { valid: false, message: 'Phone number must be 12 digits including country code' };
    }

    // Check if it matches known network prefixes
    const network = this.detectMobileNetwork(cleanPhone);
    if (!network.includes('TZ-')) {
      return { valid: false, message: 'Unsupported mobile network' };
    }

    return { valid: true };
  }

  /**
   * Format amount for Tembo API
   */
  formatAmount(amount: number): number {
    // Tembo expects amounts in minor units (e.g., cents for TZS)
    return Math.round(amount * 100) / 100;
  }

  /**
   * Get supported currencies
   */
  getSupportedCurrencies(): string[] {
    return ['TZS']; // Currently only Tanzanian Shilling
  }

  /**
   * Get supported mobile networks
   */
  getSupportedNetworks(): string[] {
    return [
      'TZ-TIGO-C2B', 'TZ-TIGO-B2C',
      'TZ-AIRTEL-C2B', 'TZ-AIRTEL-B2C',
      'TZ-VODACOM-C2B', 'TZ-VODACOM-B2C'
    ];
  }

  /**
   * Get supported bank codes for Tanzania
   */
  getSupportedBankCodes(): string[] {
    return [
      'CRDBTZTZ', // CRDB Bank
      'NMIBTZTZ', // NMB Bank
      'CORUTZTZ', // Stanbic Bank
      'FBMEUKTZ', // FBME Bank
      'TCIBTZTZ'  // Tanzania Commercial Bank
    ];
  }

  /**
   * Get network display name
   */
  getNetworkDisplayName(networkCode: string): string {
    const networkNames: Record<string, string> = {
      'TZ-TIGO-C2B': 'Tigo Pesa (Collection)',
      'TZ-TIGO-B2C': 'Tigo Pesa (Payout)',
      'TZ-AIRTEL-C2B': 'Airtel Money (Collection)',
      'TZ-AIRTEL-B2C': 'Airtel Money (Payout)',
      'TZ-VODACOM-C2B': 'M-Pesa (Collection)',
      'TZ-VODACOM-B2C': 'M-Pesa (Payout)',
      'TZ-BANK-B2C': 'Bank Transfer'
    };

    return networkNames[networkCode] || networkCode;
  }

  /**
   * Check if transaction is still pending based on status
   */
  isTransactionPending(statusCode: string | number): boolean {
    const pendingStatuses = [
      'PENDING_ACK',
      'PENDING',
      'PROCESSING',
      202,
      'PAYMENT_ACCEPTED'
    ];

    return pendingStatuses.includes(statusCode);
  }

  /**
   * Check if transaction is successful
   */
  isTransactionSuccessful(statusCode: string | number): boolean {
    const successStatuses = [
      'SUCCESS',
      'COMPLETED',
      'SUCCESSFUL',
      200,
      201,
      true
    ];

    return successStatuses.includes(statusCode);
  }

  /**
   * Check if transaction failed
   */
  isTransactionFailed(statusCode: string | number): boolean {
    return !this.isTransactionPending(statusCode) && 
           !this.isTransactionSuccessful(statusCode);
  }

  /**
   * Generate unique transaction reference
   */
  generateTransactionRef(prefix: string = 'TEMBO'): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    return `${prefix}_${timestamp}_${random}`;
  }

  /**
   * Parse Tembo date format to ISO string
   */
  parseTemboDate(temboDate: string): string {
    try {
      // Tembo typically uses 'YYYY-MM-DD HH:mm:ss' format
      const date = new Date(temboDate);
      return date.toISOString();
    } catch (error) {
      this.logger.warn('Failed to parse Tembo date', { temboDate });
      return new Date().toISOString();
    }
  }

  /**
   * Format date for Tembo API
   */
  formatDateForTembo(date: Date = new Date()): string {
    // Return format: YYYY-MM-DD HH:mm:ss
    return date.toISOString().slice(0, 19).replace('T', ' ');
  }

  /**
   * Calculate transaction fees (mock implementation)
   * In production, this should use Tembo's actual fee structure
   */
  calculateTransactionFee(amount: number, transactionType: 'COLLECTION' | 'PAYOUT' | 'BANK_TRANSFER'): number {
    // Mock fee calculation - replace with actual Tembo fee structure
    const feeRates = {
      'COLLECTION': 0.015, // 1.5%
      'PAYOUT': 0.02,      // 2.0%
      'BANK_TRANSFER': 0.025 // 2.5%
    };

    const rate = feeRates[transactionType] || 0.02;
    const fee = amount * rate;
    
    // Minimum fee of 500 TZS, maximum of 5000 TZS
    return Math.max(500, Math.min(5000, fee));
  }

  /**
   * Get transaction limits for different transaction types
   */
  getTransactionLimits(transactionType: 'COLLECTION' | 'PAYOUT' | 'BANK_TRANSFER'): {
    min: number;
    max: number;
    daily: number;
  } {
    // Mock limits - replace with actual Tembo limits
    const limits = {
      'COLLECTION': { min: 1000, max: 5000000, daily: ******** },
      'PAYOUT': { min: 1000, max: 3000000, daily: 8000000 },
      'BANK_TRANSFER': { min: 10000, max: ********, daily: ******** }
    };

    return limits[transactionType] || limits['COLLECTION'];
  }

  /**
   * Validate transaction amount against limits
   */
  validateTransactionAmount(
    amount: number, 
    transactionType: 'COLLECTION' | 'PAYOUT' | 'BANK_TRANSFER'
  ): { valid: boolean; message?: string } {
    const limits = this.getTransactionLimits(transactionType);

    if (amount < limits.min) {
      return {
        valid: false,
        message: `Amount ${amount} TZS is below minimum limit of ${limits.min} TZS for ${transactionType}`
      };
    }

    if (amount > limits.max) {
      return {
        valid: false,
        message: `Amount ${amount} TZS exceeds maximum limit of ${limits.max} TZS for ${transactionType}`
      };
    }

    return { valid: true };
  }
}