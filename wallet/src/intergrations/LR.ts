// liquidity.ts
import axios, { AxiosInstance } from 'axios';
import dotenv from 'dotenv';

dotenv.config();

const LR_API_KEY = process.env.LR_API_KEY || "";
const LR_BASE_URL = process.env.LR_BASE_URL;

export type PaymentMethodType = 'mobile_money' | 'bank';

export interface MobileMoneyPayload {
  type: 'mobile_money';
  currency: string;
  phone_number: string;
  country_code: string;
  network: string;
  account_name: string;
}

export interface WalletPayload {
  type: 'wallet';
  currency: string;
  account_name: string;
  account_number: string;
  public_key: string;
  client_id: string;
}

export interface BankPayload {
  type: 'bank';
  currency: string;
  country_code: string;
  account_name: string;
  bank_name: string;
  bank_code: string;
  account_number: string;
  sort_code: string;
  swift_code: string;
}

export type QuoteInfo = {
  provider_id: string;
  quote_id: string;
  send_asset: string;
  reference_id: string;
  send_amount: string;
  service_id: string;
  service_name: string;
  chain: string;
  source: string;
  payment_method_id: string;
};

export default class LiquidityClient {
  private http: AxiosInstance;

  constructor() {
    this.http = axios.create({
      baseURL: LR_BASE_URL,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${LR_API_KEY}`,
      },
    });
  }

  async addPaymentMethod(payload: MobileMoneyPayload | BankPayload) {
    try {
      const res = await this.http.post('/accounts/addPaymentMethod', payload);
      return res.data;
    } catch (err: any) {
      throw new Error(err.response?.data?.message || err.message);
    }
  }

  async getPaymentMethodsForCurrency(currency: string) {
    try {
      const res = await this.http.get(`/accounts/getPaymentMethodForCurrency/${currency}`);
      return res.data;
    } catch (err: any) {
      throw new Error(err.response?.data?.message || err.message);
    }
  }

  async getProviders(asset: string, currency: string) {
    const body = { asset, currency };
    const resp = await this.http.post('/accounts/provider', body);
    return resp.data;
  }

  async getRate(symbol: string, currency: string, amount: number, provider_id: number = 1) {
    const body = { symbol, currency, amount, provider_id };
    const resp = await this.http.post('/accounts/getRate', body);
    return resp.data;
  }

  async getproviderQuote(quoteId: string) {
    const resp = await this.http.get(`/accounts/getProviderQuote/${encodeURIComponent(quoteId)}`);
    return resp.data;
  }

  async createQuote(quoteInfo: QuoteInfo) {
    const resp = await this.http.post('/accounts/generateQuote', quoteInfo);
    return resp.data;
  }

  async getQuote(quoteId: string) {
    const resp = await this.http.get(`/liquidity/quote/${encodeURIComponent(quoteId)}`);
    return resp.data;
  }

  async settleQuote(quoteId: string) {
    const resp = await this.http.post('/liquidity/settle', { quoteId });
    return resp.data;
  }

  async cancelQuote(quoteId: string) {
    const resp = await this.http.post('/liquidity/cancel', { quoteId });
    return resp.data;
  }

  async sendToLiquidityRail(client_id: string, amount: number, symbol: string, currency: string, payment_method_id: number, service_id: any, provider_id: any, chain: string, reference_id: string) {

    const data = {
      company_id: client_id,
      amount: amount,
      asset_code: symbol,
      reference_id,
      currency: currency,
      service_code: service_id,
      provider_id: provider_id,
      source: "exchange"
    }

    const rateResponse = await this.http.post('/accounts/bookRate', data);
 
    const responseData: any = rateResponse.data
    const rate = responseData.data;
    console.log('Rate::::', rate);
    if (responseData.status != 200) {
      return rate;
    }

    const confirmRate = {
      client_id: client_id,
      quote_id: rate.quoteId,
      reference_id: reference_id,
      payment_method_id: payment_method_id,
      sending_address: process.env.LR_SENDING_ADDRESS || "",
      chain: chain,
      company_id: client_id,
      service_id: service_id,
      provider_id: provider_id,
      service_code: service_id,
      source: "exchange"
    }
    console.log('Confirm Rate:', confirmRate);
    const confirmRateResponse = await this.http.post('/accounts/confirmBookedRate', confirmRate);
    const confirmRateData: any = confirmRateResponse.data
    console.log('Confirm Rate:', confirmRateData);
    return confirmRateData;
  }
}
