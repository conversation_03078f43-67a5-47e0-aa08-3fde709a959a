import { getUserWallet } from "../intergrations/Utilia";
import { SweeperService } from "../services/sweeper.service";

export class SweepTest {
  private sweeper: SweeperService;

  constructor() {
    this.sweeper = new SweeperService();
  }

  /**
   * Test sweeper functionality for wallet 9486e7fb74b3
   */
  async testSweeper() {
    console.log('🧹 Testing sweeper for wallet 9486e7fb74b3...');
    
    try {
      // Dry run sweep
      const dryRunResult = await this.sweeper.sweepFromWallet({
        walletId: '9486e7fb74b3',
        destinationWalletId: '9a185f915491',
        minAmount: '2',
        dryRun: true,
        memo: 'Test sweep from 9486e7fb74b3',
        includeAssets: ['USDT', 'USDC']
      });
      
      console.log('Dry run result:', dryRunResult);
      
      // Actual sweep (if dry run shows funds)
      if (dryRunResult.success && dryRunResult.summary.successfulSweeps > 0) {
        const actualResult = await this.sweeper.sweepFromWallet({
          walletId: '9486e7fb74b3',
          destinationWalletId: '9a185f915491',
          minAmount: '2',
          dryRun: false,
          memo: 'Sweep from 9486e7fb74b3 to cold storage',
          includeAssets: ['USDT', 'USDC']
        });
        
        console.log('Actual sweep result:', actualResult);
      }
      
    } catch (error: any) {
      console.error('Test failed:', error.message);
    }
  }

  /**
   * Test sweeping from all wallets (only those starting with '10')
   */
  async testSweepFromAllWallets() {
    console.log('🧹 Testing sweep from all wallets...');
    
    try {
      const result = await this.sweeper.sweepFromAllWallets({
        minAmount: '2',
        dryRun: true,
        memo: 'Automated sweep to cold storage',
        includeAssets: ['USDT', 'USDC']
      });

      console.log('Sweep result:', result);

    } catch (error: any) {
      console.error('Test failed:', error.message);
    }
  }

  /**
   * Test getting sweep history and statistics
   */
  async testSweepHistoryAndStats() {
    console.log('📊 Testing sweep history and statistics...');
    
    try {
      const history = await this.sweeper.getSweepHistory(10);
      const stats = await this.sweeper.getSweepStats();
      const lastRun = await this.sweeper.getLastSweepRun();
      
      console.log('History:', history);
      console.log('Stats:', stats);
      console.log('Last run:', lastRun);

    } catch (error: any) {
      console.error('Test failed:', error.message);
    }
  }

  /**
   * Check destination wallet addresses
   */
  async checkDestinationWallet() {
    console.log('🔍 Checking destination wallet addresses...');
    
    try {
      const walletResponse = await getUserWallet({ walletId: '9a185f915491' });
      
      if (walletResponse.error) {
        console.error('Failed to get destination wallet:', walletResponse.error);
        return;
      }
      
      const wallet = walletResponse.data.wallet;
      console.log('Destination wallet details:', {
        name: wallet.displayName,
        id: wallet.name.split('/').pop(),
        networks: wallet.networks || [],
        evmDetails: wallet.evmDetails,
        btcDetails: wallet.btcDetails,
        tronDetails: wallet.tronDetails,
        solanaDetails: wallet.solanaDetails
      });
      
      // Check if it has addresses for the required networks
      const requiredNetworks = [
        'networks/bnb-smart-chain-mainnet',
        'networks/base-mainnet'
      ];
      
      console.log('\nRequired networks for sweeping:');
      requiredNetworks.forEach(network => {
        const hasNetwork = wallet.networks?.includes(network);
        console.log(`  ${network}: ${hasNetwork ? '✅' : '❌'}`);
      });
      
    } catch (error: any) {
      console.error('Check failed:', error.message);
    }
  }

  /**
   * Run all sweeper tests
   */
  async runAllTests() {
    console.log('🚀 Starting Sweeper Service Tests...\n');
    
    await this.testSweeper();
    console.log('\n' + '='.repeat(50) + '\n');
    
    await this.testSweepFromAllWallets();
    console.log('\n' + '='.repeat(50) + '\n');
    
    await this.testSweepHistoryAndStats();
    
    console.log('\n✅ All Sweeper tests completed!');
  }

  /**
   * Run specific test
   */
  async runTest(testName: 'sweeper' | 'allWallets' | 'history' | 'destination' | 'all') {
    switch (testName) {
      case 'sweeper':
        await this.testSweeper();
        break;
      case 'allWallets':
        await this.testSweepFromAllWallets();
        break;
      case 'history':
        await this.testSweepHistoryAndStats();
        break;
      case 'destination':
        await this.checkDestinationWallet();
        break;
      case 'all':
        await this.runAllTests();
        break;
      default:
        console.log('❌ Unknown test name. Available tests: sweeper, allWallets, history, destination, all');
    }
  }
}

// Example usage:
// const test = new SweepTest();
// test.runTest('sweeper'); // Run specific test
// test.runAllTests(); // Run all tests 