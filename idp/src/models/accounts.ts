import Model from "../helpers/model";
import { v4 as uuidv4 } from "uuid";
import CryptoJS from "crypto-js";
import jwt from "jsonwebtoken";

const SECRET_KEY = process.env.SECRET_KEY || "your-secret-key";

interface UserRegistration {
  email: string;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  password: string;
}

interface LoginCredentials {
  email: string;
  password: string;
}

class Accounts extends Model {
  constructor() {
    super();
  }

  async register(data: UserRegistration) {
    try {
      const existingUser = await this.selectDataQuery("users", `email = '${data.email}'`);
      if (existingUser.length > 0) {
        return this.makeResponse(409, "<PERSON><PERSON> already registered");
      }

      const userId = uuidv4();
      const hashedPassword = this.hashPassword(data.password);
      const otp = await this.generateOTP();

      const userData = {
        id: userId,
        email: data.email,
        first_name: data.firstName,
        last_name: data.lastName,
        phone_number: data.phoneNumber,
        password: hashedPassword,
        status: 'pending',
        email_verified: false,
        created_at: new Date()
      };

      await this.insertData("users", userData);
      await this.storeOTP(data.email, otp, userId);

      return this.makeResponse(201, "Registration successful. Please verify your email.", {
        userId,
        email: data.email
      });
    } catch (error) {
      console.error("Registration error:", error);
      return this.makeResponse(500, "Registration failed");
    }
  }

  async login(credentials: LoginCredentials) {
    try {
      const hashedPassword = this.hashPassword(credentials.password);
      const user = await this.selectDataQuery(
        "users",
        `email = '${credentials.email}' AND password = '${hashedPassword}'`
      );

      if (user.length === 0) {
        return this.makeResponse(401, "Invalid credentials");
      }

      if (user[0].status !== 'active') {
        return this.makeResponse(403, "Account not verified or inactive");
      }

      const token = this.generateToken(user[0].id);
      return this.makeResponse(200, "Login successful", {
        token,
        user: {
          id: user[0].id,
          email: user[0].email,
          firstName: user[0].first_name,
          lastName: user[0].last_name
        }
      });
    } catch (error) {
      console.error("Login error:", error);
      return this.makeResponse(500, "Login failed");
    }
  }

  async verifyEmail(data: any) {
    try {
      const { email, otp } = data;

      const otpRecord = await this.selectDataQuery(
        "user_otp",
        `email = '${email}' AND otp = '${otp}' AND expires_at > NOW()`
      );

      if (otpRecord.length === 0) {
        return this.makeResponse(400, "Invalid or expired OTP");
      }

      await this.updateData(
        "users",
        `id = '${otpRecord[0].user_id}'`,
        {
          status: 'active',
          email_verified: true
        }
      );

      await this.deleteData("user_otp", `email = '${email}'`);
      return this.makeResponse(200, "Email verified successfully");
    } catch (error) {
      console.error("Email verification error:", error);
      return this.makeResponse(500, "Email verification failed");
    }
  }

  async requestPasswordReset(email: string) {
    try {
      const user = await this.selectDataQuery("users", `email = '${email}'`);
      if (user.length === 0) {
        return this.makeResponse(404, "User not found");
      }

      const otp = await this.generateOTP();
      await this.storeOTP(email, otp, user[0].id);

      return this.makeResponse(200, "Password reset OTP sent successfully");
    } catch (error) {
      console.error("Password reset request error:", error);
      return this.makeResponse(500, "Password reset request failed");
    }
  }

  async resetPassword(data: any) {
    try {
      const { email, otp, newPassword } = data;
      const otpRecord = await this.selectDataQuery(
        "user_otp",
        `email = '${email}' AND otp = '${otp}' AND expires_at > NOW()`
      );

      if (otpRecord.length === 0) {
        return this.makeResponse(400, "Invalid or expired OTP");
      }

      const hashedPassword = this.hashPassword(newPassword);
      await this.updateData(
        "users",
        `id = '${otpRecord[0].user_id}'`,
        { password: hashedPassword }
      );

      await this.deleteData("user_otp", `email = '${email}'`);
      return this.makeResponse(200, "Password reset successfully");
    } catch (error) {
      console.error("Password reset error:", error);
      return this.makeResponse(500, "Password reset failed");
    }
  }

  async changepassword(data: any) {
    try {
      const { clientId, current_password, new_password, confirm_new_password } = data;
      const encryptedCurrent = CryptoJS.AES.encrypt(current_password, SECRET_KEY).toString();
      const encryptedNew = CryptoJS.AES.encrypt(new_password, SECRET_KEY).toString();

      if (new_password !== confirm_new_password) {
        return this.makeResponse(400, "Passwords don't match");
      }

      const user = await this.selectDataQuery("user_credentials", `client_id = '${clientId}' and password='${encryptedCurrent}'`);
      if (user.length === 0) {
        return this.makeResponse(404, "Invalid current password");
      }

      await this.updateData("user_credentials", `email = '${clientId}'`, { password: encryptedNew });
      this.sendEmail("PASSWORD_CHANGED", user[0].email);

      return this.makeResponse(200, "Password reset successfully");
    } catch (error: any) {
      console.error("Error resetting password:", error);
      return this.makeResponse(500, "Error resetting password");
    }
  }

  async getAllusers() {
    try {
      const users = await this.selectDataQuery(
        "users",
        "status = 'active'"
      );
      return this.makeResponse(200, "Users retrieved successfully", users);
    } catch (error) {
      console.error("Error fetching users:", error);
      return this.makeResponse(500, "Error fetching users");
    }
  }

  async getusersByProfile(profileId: string) {
    try {
      const users = await this.selectDataQuery(
        "users",
        `profile_id = '${profileId}' AND status = 'active'`)
      
      return this.makeResponse(200, "Profile users retrieved successfully", users);
    } catch (error) {
      console.error("Error fetching profile users:", error);
      return this.makeResponse(500, "Error fetching profile users");
    }
  }

  async createUserLogin(data: any) {
    try {
      const { email, first_name, last_name, client_id } = data;
      const password = this.getRandomString();

      const existingAdmin = await this.selectDataQuery("users", `client_id = '${client_id}'`);
      if (existingAdmin.length === 0) {
        return this.makeResponse(409, "Client doesn't exist");
      }

      const encryptedPassword = CryptoJS.AES.encrypt(password, SECRET_KEY).toString();

      await this.insertData("user_credentials", {
        client_id,
        email,
        password: encryptedPassword,
        role: "admin",
        first_name,
        last_name,
      });

      this.sendEmail("CLIENT_REGISTRATION", email, first_name, password);
      return this.makeResponse(201, "Client access created successfully");
    } catch (error: any) {
      console.error("Error creating admin user:", error);
      return this.makeResponse(500, "Email already exists");
    }
  }

  async getProfile(clientId: string) {
    return this.selectDataQuery("users", `client_id = '${clientId}'`);
  }

  private generateToken(userId: string): string {
    return jwt.sign({ userId }, SECRET_KEY, { expiresIn: '24h' });
  }

  private hashPassword(password: string): string {
    return CryptoJS.SHA256(password).toString();
  }

  private async generateOTP(): Promise<string> {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  private async storeOTP(email: string, otp: string, userId: string): Promise<void> {
    const expiresAt = new Date();
    expiresAt.setMinutes(expiresAt.getMinutes() + 10);
    await this.insertData("user_otp", {
      email,
      otp,
      user_id: userId,
      expires_at: expiresAt
    });
  }
}

export default Accounts;
