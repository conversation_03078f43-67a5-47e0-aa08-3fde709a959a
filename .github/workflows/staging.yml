name: Deploy to Remote Server (Staging)

on:
  push:
    branches:
      - stage  
      # - stagg-deploy

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Add SSH Key
        uses: webfactory/ssh-agent@v0.7.0
        with:
          ssh-private-key: ${{ secrets.SERVER_PRIVATE_KEY }}

      - name: Copy code to remote server (safe, no delete)
        run: |
          rsync -avz -e "ssh -o StrictHostKeyChecking=no" \
            ./ ${{ secrets.SERVER_USER }}@${{ secrets.SERVER_HOST }}:/home/<USER>/deployments

      - name: Run docker-compose on remote server
        run: |
          ssh -o StrictHostKeyChecking=no ${{ secrets.SERVER_USER }}@${{ secrets.SERVER_HOST }} << 'EOF'
          cd /home/<USER>/deployments
          docker-compose build agg
          docker-compose build rail
          docker-compose build agg-admn
          docker-compose up -d agg
          docker-compose up -d rail
          docker-compose up -d agg-admin
          EOF
